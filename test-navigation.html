<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .navigation-dropdown {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        .dropdown-trigger {
            background: #dc2626;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 40px;
            box-sizing: border-box;
        }
        .dropdown-trigger:hover {
            background: #b91c1c;
        }
        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            margin-top: 2px;
        }
        .dropdown-item {
            padding: 12px 16px;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        .dropdown-item:last-child {
            border-bottom: none;
        }
        .dropdown-item:hover {
            background: #f9fafb;
        }
        .test-info {
            background: #f0f9ff;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
            color: #0369a1;
        }
    </style>
</head>
<body>
    <h1>导航功能测试</h1>
    
    <div class="test-section">
        <div class="test-info">
            测试地址：辽宁省大连市中山区海军广场街道乐华街32号<br>
            医院名称：大连东海医院
        </div>
        
        <div class="navigation-dropdown">
            <button class="dropdown-trigger" onclick="toggleDropdown(this)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
                </svg>
                到这去
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 4px;">
                    <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
            </button>
            <div class="dropdown-menu">
                <div class="dropdown-item" onclick="testBaiduNavigation(); hideDropdown()">
                    百度地图导航
                </div>
                <div class="dropdown-item" onclick="testGaodeNavigation(); hideDropdown()">
                    高德地图导航
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>测试结果</h3>
        <div id="test-results"></div>
    </div>

    <script>
        const address = "辽宁省大连市中山区海军广场街道乐华街32号";
        const hospitalName = "大连东海医院";
        const lat = 38.9140; // 示例坐标
        const lng = 121.6147; // 示例坐标

        function toggleDropdown(button) {
            const dropdown = button.parentNode;
            const menu = dropdown.querySelector('.dropdown-menu');
            const isVisible = menu.style.display === 'block';
            
            // 隐藏所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(m => m.style.display = 'none');
            
            // 切换当前下拉菜单
            menu.style.display = isVisible ? 'none' : 'block';
        }
        
        function hideDropdown() {
            document.querySelectorAll('.dropdown-menu').forEach(m => m.style.display = 'none');
        }
        
        // 点击外部区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navigation-dropdown')) {
                hideDropdown();
            }
        });

        function testBaiduNavigation() {
            const baiduMapUrl = `baidumap://map/direction?destination=${encodeURIComponent(address)}&mode=driving&src=大连东海医院官网`;
            
            logResult('百度地图导航', baiduMapUrl);
            
            try {
                window.location.href = baiduMapUrl;
                setTimeout(() => {
                    const webUrl = `https://map.baidu.com/search/${encodeURIComponent(address)}`;
                    window.open(webUrl, '_blank');
                }, 2000);
            } catch (error) {
                console.error('百度地图导航失败:', error);
                const webUrl = `https://map.baidu.com/search/${encodeURIComponent(address)}`;
                window.open(webUrl, '_blank');
            }
        }

        function testGaodeNavigation() {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            
            let gaodeMapUrl;
            if (isIOS) {
                gaodeMapUrl = `iosamap://navi?sourceApplication=大连东海医院官网&poiname=${encodeURIComponent(hospitalName)}&lat=${lat}&lon=${lng}&dev=0&style=2`;
            } else {
                gaodeMapUrl = `androidamap://navi?sourceApplication=大连东海医院官网&poiname=${encodeURIComponent(hospitalName)}&lat=${lat}&lon=${lng}&dev=0&style=2`;
            }
            
            logResult('高德地图导航', gaodeMapUrl);
            
            try {
                window.location.href = gaodeMapUrl;
                setTimeout(() => {
                    const webUrl = `https://uri.amap.com/navigation?to=${lng},${lat},${encodeURIComponent(address)}&src=大连东海医院官网&callnative=1`;
                    window.open(webUrl, '_blank');
                }, 2000);
            } catch (error) {
                console.error('高德地图导航失败:', error);
                const webUrl = `https://uri.amap.com/navigation?to=${lng},${lat},${encodeURIComponent(address)}&src=大连东海医院官网&callnative=1`;
                window.open(webUrl, '_blank');
            }
        }

        function logResult(type, url) {
            const results = document.getElementById('test-results');
            const time = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="margin: 5px 0; padding: 5px; background: #f8f9fa; border-radius: 3px; font-size: 12px;">
                <strong>${time} - ${type}</strong><br>
                <code style="word-break: break-all;">${url}</code>
            </div>`;
        }
    </script>
</body>
</html>
