#!/usr/bin/env node

/**
 * 微信公众号API爬虫
 * 通过微信公众号平台API获取文章列表
 */

const fs = require('fs').promises;
const path = require('path');

// 动态导入node-fetch
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// 配置
const CONFIG = {
  // 目标公众号信息
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  
  // 微信公众号平台配置
  wechatApi: {
    // 需要从微信公众号平台获取
    appId: process.env.WECHAT_APP_ID || '',
    appSecret: process.env.WECHAT_APP_SECRET || '',
    // 获取access_token的URL
    tokenUrl: 'https://api.weixin.qq.com/cgi-bin/token',
    // 获取素材列表的URL
    materialUrl: 'https://api.weixin.qq.com/cgi-bin/material/batchget_material'
  },
  
  // 输出配置
  outputDir: '../src/data/wechat',
  maxArticles: 50,
  
  // 请求配置
  timeout: 15000
};

// 获取微信access_token
async function getAccessToken() {
  if (!CONFIG.wechatApi.appId || !CONFIG.wechatApi.appSecret) {
    throw new Error('缺少微信公众号AppID和AppSecret配置');
  }

  const url = `${CONFIG.wechatApi.tokenUrl}?grant_type=client_credential&appid=${CONFIG.wechatApi.appId}&secret=${CONFIG.wechatApi.appSecret}`;
  
  const response = await fetch(url);
  const data = await response.json();
  
  if (data.errcode) {
    throw new Error(`获取access_token失败: ${data.errmsg}`);
  }
  
  return data.access_token;
}

// 获取图文素材列表
async function getMaterialList(accessToken, offset = 0, count = 20) {
  const url = `${CONFIG.wechatApi.materialUrl}?access_token=${accessToken}`;
  
  const requestBody = {
    type: 'news',  // 图文消息
    offset: offset,
    count: count
  };
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestBody)
  });
  
  const data = await response.json();
  
  if (data.errcode) {
    throw new Error(`获取素材列表失败: ${data.errmsg}`);
  }
  
  return data;
}

// 处理文章数据
function processArticles(materialData) {
  const articles = [];
  
  if (!materialData.item) {
    return articles;
  }
  
  materialData.item.forEach((item, index) => {
    const content = item.content;
    if (content && content.news_item) {
      content.news_item.forEach((newsItem, newsIndex) => {
        const article = {
          id: `wechat_api_${item.media_id}_${newsIndex}`,
          title: newsItem.title,
          link: newsItem.url,
          summary: newsItem.digest || '',
          publishDate: new Date(item.update_time * 1000).toLocaleDateString('zh-CN'),
          source: 'API获取',
          category: categorizeArticle(newsItem.title),
          crawlTime: new Date().toISOString(),
          account: CONFIG.wechatAccount.name,
          image: newsItem.thumb_url,
          mediaId: item.media_id,
          author: newsItem.author || CONFIG.wechatAccount.name
        };
        
        articles.push(article);
      });
    }
  });
  
  return articles;
}

// 根据标题自动分类
function categorizeArticle(title) {
  if (title.includes('科普') || title.includes('药食同源') || title.includes('中医')) {
    return 'education';
  } else if (title.includes('健康') || title.includes('养生') || title.includes('饮食')) {
    return 'health';
  } else if (title.includes('医院') || title.includes('科室') || title.includes('专家')) {
    return 'hospital';
  } else {
    return 'news';
  }
}

// 保存文章数据
async function saveArticles(articles) {
  try {
    // 确保输出目录存在
    const outputPath = path.resolve(__dirname, CONFIG.outputDir);
    await fs.mkdir(outputPath, { recursive: true });
    
    // 保存到不同文件
    const categorizedArticles = {
      all: articles,
      news: articles.filter(a => a.category === 'news'),
      health: articles.filter(a => a.category === 'health'),
      education: articles.filter(a => a.category === 'education'),
      hospital: articles.filter(a => a.category === 'hospital')
    };
    
    for (const [category, categoryArticles] of Object.entries(categorizedArticles)) {
      const filename = `wechat-${category}.json`;
      const filepath = path.join(outputPath, filename);
      
      const data = {
        updateTime: new Date().toISOString(),
        account: CONFIG.wechatAccount,
        category: category,
        total: categoryArticles.length,
        articles: categoryArticles
      };
      
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      console.log(`💾 保存 ${category} 类文章: ${categoryArticles.length} 篇 -> ${filename}`);
    }
    
    console.log(`✅ 文章数据保存完成，输出目录: ${outputPath}`);
    
  } catch (error) {
    console.error(`❌ 保存文章失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始通过API获取微信公众号文章');
  console.log(`📱 目标账号: ${CONFIG.wechatAccount.name} (${CONFIG.wechatAccount.wechatId})`);
  console.log('=' .repeat(60));
  
  try {
    // 确保fetch已加载
    if (!fetch) {
      const { default: nodeFetch } = await import('node-fetch');
      fetch = nodeFetch;
    }
    
    // 获取access_token
    console.log('🔑 获取access_token...');
    const accessToken = await getAccessToken();
    console.log('✅ access_token获取成功');
    
    // 获取文章列表
    console.log('📰 获取文章列表...');
    let allArticles = [];
    let offset = 0;
    const batchSize = 20;
    
    while (allArticles.length < CONFIG.maxArticles) {
      const materialData = await getMaterialList(accessToken, offset, batchSize);
      const articles = processArticles(materialData);
      
      if (articles.length === 0) {
        console.log('📄 没有更多文章了');
        break;
      }
      
      allArticles = allArticles.concat(articles);
      offset += batchSize;
      
      console.log(`📊 已获取 ${allArticles.length} 篇文章`);
      
      // 如果返回的文章数少于请求数，说明已经到底了
      if (articles.length < batchSize) {
        break;
      }
    }
    
    // 限制文章数量
    if (allArticles.length > CONFIG.maxArticles) {
      allArticles = allArticles.slice(0, CONFIG.maxArticles);
    }
    
    console.log(`\n📊 总共获取文章: ${allArticles.length} 篇`);
    
    // 保存文章
    await saveArticles(allArticles);
    
    console.log('\n🎉 API获取任务完成！');
    
  } catch (error) {
    console.error(`❌ API获取失败: ${error.message}`);
    
    // 如果是配置问题，提供帮助信息
    if (error.message.includes('AppID') || error.message.includes('AppSecret')) {
      console.log('\n📋 配置帮助:');
      console.log('1. 登录微信公众号平台: https://mp.weixin.qq.com');
      console.log('2. 进入"开发" -> "基本配置"');
      console.log('3. 获取AppID和AppSecret');
      console.log('4. 设置环境变量:');
      console.log('   export WECHAT_APP_ID="your_app_id"');
      console.log('   export WECHAT_APP_SECRET="your_app_secret"');
    }
    
    process.exit(1);
  }
}

// 执行爬虫
if (require.main === module) {
  main();
}

module.exports = { 
  getAccessToken,
  getMaterialList,
  processArticles,
  saveArticles,
  CONFIG 
};
