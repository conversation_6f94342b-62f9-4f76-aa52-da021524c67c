const axios = require('axios');
const Logger = require('./logger');

class HttpClient {
  constructor(config = {}) {
    this.logger = new Logger();
    this.config = {
      timeout: config.timeout || 30000,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
      userAgents: config.userAgents || [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      ]
    };
  }

  getRandomUserAgent() {
    const agents = this.config.userAgents;
    return agents[Math.floor(Math.random() * agents.length)];
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async request(url, options = {}) {
    const config = {
      url,
      method: options.method || 'GET',
      timeout: this.config.timeout,
      headers: {
        'User-Agent': this.getRandomUserAgent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...options.headers
      },
      ...options
    };

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        this.logger.debug(`请求 ${url} (尝试 ${attempt}/${this.config.retryAttempts})`);
        
        const response = await axios(config);
        
        this.logger.debug(`请求成功: ${url}`, {
          status: response.status,
          contentLength: response.data?.length || 0
        });
        
        return response;
      } catch (error) {
        this.logger.warn(`请求失败 (尝试 ${attempt}/${this.config.retryAttempts}): ${url}`, {
          error: error.message,
          status: error.response?.status
        });

        if (attempt === this.config.retryAttempts) {
          this.logger.error(`请求最终失败: ${url}`, error.message);
          throw error;
        }

        // 等待后重试
        await this.delay(this.config.retryDelay * attempt);
      }
    }
  }

  async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  async post(url, data, options = {}) {
    return this.request(url, { ...options, method: 'POST', data });
  }
}

module.exports = HttpClient;
