#!/bin/bash

# 微信公众号文章自动更新脚本
# 支持多种获取方式：API、网页爬虫、手动导入

set -e  # 遇到错误立即退出

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$SCRIPT_DIR/wechat-update.log"
DATA_DIR="$PROJECT_ROOT/src/data/wechat"

# 爬虫方式配置
CRAWLER_METHOD="${WECHAT_CRAWLER_METHOD:-web}"  # api, web, manual

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        error_exit "Node.js 未安装"
    fi
    
    if ! command -v pnpm &> /dev/null; then
        error_exit "pnpm 未安装"
    fi
    
    log "依赖检查完成"
}

# 备份现有数据
backup_data() {
    log "备份现有数据..."
    
    if [ -d "$DATA_DIR" ]; then
        BACKUP_DIR="$SCRIPT_DIR/backups/$(date '+%Y%m%d_%H%M%S')"
        mkdir -p "$BACKUP_DIR"
        cp -r "$DATA_DIR"/* "$BACKUP_DIR/" 2>/dev/null || true
        log "数据已备份到: $BACKUP_DIR"
    else
        log "没有现有数据需要备份"
    fi
}

# 运行爬虫
run_crawler() {
    log "开始运行微信文章爬虫 (方式: $CRAWLER_METHOD)..."

    cd "$SCRIPT_DIR"

    local crawler_script=""
    case "$CRAWLER_METHOD" in
        "api")
            crawler_script="wechat-api-crawler.js"
            log "使用微信公众号API方式"
            ;;
        "web")
            crawler_script="wechat-web-crawler.js"
            log "使用网页爬虫方式"
            ;;
        "manual")
            log "手动导入模式，跳过自动爬取"
            return 0
            ;;
        *)
            log "未知的爬虫方式: $CRAWLER_METHOD，使用默认网页爬虫"
            crawler_script="wechat-web-crawler.js"
            ;;
    esac

    # 检查爬虫脚本是否存在
    if [ ! -f "$crawler_script" ]; then
        log "ERROR: 爬虫脚本不存在: $crawler_script"
        return 1
    fi

    # 运行爬虫脚本
    if node "$crawler_script" >> "$LOG_FILE" 2>&1; then
        log "爬虫运行成功"
        return 0
    else
        log "爬虫运行失败，尝试备用方案"

        # 如果API失败，尝试网页爬虫
        if [ "$CRAWLER_METHOD" = "api" ] && [ -f "wechat-web-crawler.js" ]; then
            log "API方式失败，尝试网页爬虫方式"
            if node wechat-web-crawler.js >> "$LOG_FILE" 2>&1; then
                log "备用网页爬虫成功"
                return 0
            fi
        fi

        # 如果网页爬虫失败，使用现有数据
        if [ -f "$DATA_DIR/wechat-all.json" ]; then
            log "爬虫失败，保持现有数据不变"
            return 0
        fi

        log "所有爬虫方式都失败"
        return 1
    fi
}

# 验证数据
validate_data() {
    log "验证生成的数据..."
    
    local all_file="$DATA_DIR/wechat-all.json"
    
    if [ ! -f "$all_file" ]; then
        error_exit "主数据文件不存在: $all_file"
    fi
    
    # 检查JSON格式
    if ! node -e "JSON.parse(require('fs').readFileSync('$all_file', 'utf8'))" 2>/dev/null; then
        error_exit "主数据文件JSON格式无效"
    fi
    
    # 检查文章数量
    local article_count=$(node -e "console.log(JSON.parse(require('fs').readFileSync('$all_file', 'utf8')).total)")
    
    if [ "$article_count" -eq 0 ]; then
        log "WARNING: 没有找到文章数据"
    else
        log "验证通过: 找到 $article_count 篇文章"
    fi
}

# 更新Git忽略文件
update_gitignore() {
    log "更新.gitignore文件..."
    
    local gitignore_file="$PROJECT_ROOT/.gitignore"
    local wechat_pattern="src/data/wechat/*.json"
    
    if [ -f "$gitignore_file" ]; then
        if ! grep -q "$wechat_pattern" "$gitignore_file"; then
            echo "" >> "$gitignore_file"
            echo "# 微信公众号文章数据 (动态生成)" >> "$gitignore_file"
            echo "$wechat_pattern" >> "$gitignore_file"
            log "已添加微信数据文件到.gitignore"
        else
            log ".gitignore已包含微信数据文件规则"
        fi
    else
        log "WARNING: .gitignore文件不存在"
    fi
}

# 生成更新报告
generate_report() {
    log "生成更新报告..."
    
    local report_file="$SCRIPT_DIR/wechat-update-report.json"
    local all_file="$DATA_DIR/wechat-all.json"
    
    if [ -f "$all_file" ]; then
        local update_time=$(node -e "console.log(JSON.parse(require('fs').readFileSync('$all_file', 'utf8')).updateTime)")
        local total_articles=$(node -e "console.log(JSON.parse(require('fs').readFileSync('$all_file', 'utf8')).total)")
        
        cat > "$report_file" << EOF
{
  "lastUpdate": "$update_time",
  "totalArticles": $total_articles,
  "updateScript": "$(basename "$0")",
  "logFile": "$LOG_FILE",
  "dataDirectory": "$DATA_DIR",
  "status": "success"
}
EOF
        
        log "更新报告已生成: $report_file"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份..."
    
    local backup_base="$SCRIPT_DIR/backups"
    
    if [ -d "$backup_base" ]; then
        # 保留最近7天的备份
        find "$backup_base" -type d -name "20*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
        log "旧备份清理完成"
    fi
}

# 主函数
main() {
    log "=========================================="
    log "开始微信公众号文章更新任务"
    log "=========================================="
    
    # 检查依赖
    check_dependencies
    
    # 备份现有数据
    backup_data
    
    # 运行爬虫
    if run_crawler; then
        # 验证数据
        validate_data
        
        # 更新Git忽略文件
        update_gitignore
        
        # 生成报告
        generate_report
        
        # 清理旧备份
        cleanup_old_backups
        
        log "微信文章更新任务完成"
        log "=========================================="
        
    else
        error_exit "爬虫运行失败，任务终止"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
微信公众号文章自动更新脚本

用法: $0 [选项]

选项:
  -h, --help     显示此帮助信息
  -v, --verbose  详细输出模式
  -d, --dry-run  试运行模式（不实际更新数据）

示例:
  $0                # 正常运行更新任务
  $0 -v            # 详细输出模式
  $0 --dry-run     # 试运行模式

日志文件: $LOG_FILE
数据目录: $DATA_DIR

EOF
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x  # 启用详细输出
            shift
            ;;
        -d|--dry-run)
            log "试运行模式 - 不会实际更新数据"
            # 可以在这里添加试运行逻辑
            exit 0
            ;;
        *)
            log "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
