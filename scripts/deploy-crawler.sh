#!/bin/bash

# 微信文章爬虫部署脚本
# 使用方法: ./deploy-crawler.sh [安装目录]

set -e

# 默认安装目录
INSTALL_DIR=${1:-"/opt/wechat-crawler"}
WEBSITE_DATA_DIR=${2:-"/var/www/html/src/data/dynamic"}
LOG_DIR="/var/log"

echo "🚀 开始部署微信文章爬虫..."
echo "📁 安装目录: $INSTALL_DIR"
echo "📁 网站数据目录: $WEBSITE_DATA_DIR"

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "❌ 此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，请先安装 pnpm"
    echo "安装命令: npm install -g pnpm"
    exit 1
fi

echo "✅ 环境检查通过"

# 创建安装目录
echo "📁 创建安装目录..."
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# 创建网站数据目录
echo "📁 创建网站数据目录..."
mkdir -p "$WEBSITE_DATA_DIR"

# 复制爬虫文件
echo "📋 复制爬虫文件..."
if [ -f "../scripts/crawler.js" ]; then
    cp "../scripts/crawler.js" "$INSTALL_DIR/"
    cp "../scripts/package.json" "$INSTALL_DIR/"
else
    echo "❌ 找不到爬虫文件，请确保在项目根目录运行此脚本"
    exit 1
fi

# 修改爬虫脚本中的数据目录路径
echo "🔧 配置数据目录路径..."
sed -i "s|dataDir: '../src/data/dynamic'|dataDir: '$WEBSITE_DATA_DIR'|g" "$INSTALL_DIR/crawler.js"

# 安装依赖
echo "📦 安装依赖..."
pnpm install --prod

# 设置文件权限
echo "🔐 设置文件权限..."
chmod +x "$INSTALL_DIR/crawler.js"
chown -R www-data:www-data "$WEBSITE_DATA_DIR"
chmod 755 "$WEBSITE_DATA_DIR"

# 创建日志文件
echo "📝 创建日志文件..."
touch "$LOG_DIR/wechat-crawler.log"
chown www-data:www-data "$LOG_DIR/wechat-crawler.log"

# 测试爬虫
echo "🧪 测试爬虫..."
cd "$INSTALL_DIR"
if node crawler.js --test; then
    echo "✅ 爬虫测试成功"
else
    echo "⚠️  爬虫测试失败，但继续安装..."
fi

# 设置定时任务
echo "⏰ 设置定时任务..."
CRON_JOB="0 2 * * * cd $INSTALL_DIR && node crawler.js >> $LOG_DIR/wechat-crawler.log 2>&1"

# 检查是否已存在相同的定时任务
if crontab -l 2>/dev/null | grep -q "wechat-crawler"; then
    echo "⚠️  定时任务已存在，跳过添加"
else
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "✅ 定时任务添加成功 (每天凌晨2点执行)"
fi

# 创建手动执行脚本
echo "📜 创建手动执行脚本..."
cat > "$INSTALL_DIR/run-crawler.sh" << EOF
#!/bin/bash
cd "$INSTALL_DIR"
echo "开始执行爬虫..."
node crawler.js
echo "爬虫执行完成"
EOF

chmod +x "$INSTALL_DIR/run-crawler.sh"

# 创建状态检查脚本
echo "📊 创建状态检查脚本..."
cat > "$INSTALL_DIR/check-status.sh" << EOF
#!/bin/bash

echo "=== 微信文章爬虫状态检查 ==="
echo

echo "📁 数据目录: $WEBSITE_DATA_DIR"
echo "📝 日志文件: $LOG_DIR/wechat-crawler.log"
echo

echo "📊 数据文件状态:"
for file in news.json tcm-science.json therapy-methods.json; do
    if [ -f "$WEBSITE_DATA_DIR/\$file" ]; then
        size=\$(stat -c%s "$WEBSITE_DATA_DIR/\$file")
        modified=\$(stat -c%y "$WEBSITE_DATA_DIR/\$file")
        echo "  ✅ \$file (大小: \${size}字节, 修改时间: \$modified)"
    else
        echo "  ❌ \$file (文件不存在)"
    fi
done

echo

echo "📝 最近10条日志:"
tail -10 "$LOG_DIR/wechat-crawler.log" 2>/dev/null || echo "  无日志记录"

echo

echo "⏰ 定时任务状态:"
if crontab -l 2>/dev/null | grep -q "wechat-crawler"; then
    echo "  ✅ 定时任务已设置"
    crontab -l | grep "wechat-crawler"
else
    echo "  ❌ 定时任务未设置"
fi
EOF

chmod +x "$INSTALL_DIR/check-status.sh"

echo
echo "🎉 微信文章爬虫部署完成！"
echo
echo "📋 使用说明:"
echo "  手动执行: $INSTALL_DIR/run-crawler.sh"
echo "  状态检查: $INSTALL_DIR/check-status.sh"
echo "  查看日志: tail -f $LOG_DIR/wechat-crawler.log"
echo
echo "📁 重要目录:"
echo "  爬虫程序: $INSTALL_DIR"
echo "  数据文件: $WEBSITE_DATA_DIR"
echo "  日志文件: $LOG_DIR/wechat-crawler.log"
echo
echo "⏰ 定时任务: 每天凌晨2点自动执行"
echo
echo "🔧 如需修改配置，请编辑: $INSTALL_DIR/crawler.js"
