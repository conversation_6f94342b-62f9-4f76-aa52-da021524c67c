#!/usr/bin/env node

const cheerio = require('cheerio');
const fs = require('fs').promises;
const path = require('path');

// 动态导入node-fetch (ES模块)
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// 配置
const CONFIG = {
  // 搜狗微信搜索URL模板
  searchTemplate: 'https://weixin.sogou.com/weixin?type=1&query={keyword}',
  
  // 目标配置
  targets: [
    {
      keyword: '大连东海医院',
      filename: 'news.json',
      category: '医院新闻',
      categoryId: 'hospital-news',
      maxArticles: 20
    },
    {
      keyword: '中医科普',
      filename: 'tcm-science.json', 
      category: '中医科普',
      categoryId: 'tcm-science',
      maxArticles: 20
    },
    {
      keyword: '特色疗法',
      filename: 'therapy-methods.json',
      category: '特色疗法', 
      categoryId: 'therapy',
      maxArticles: 20
    }
  ],
  
  // 请求设置
  requestDelay: 3000,
  timeout: 15000,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  
  // 文件路径
  dataDir: '../src/data/dynamic',
  cacheDir: '../src/data/cache',
  logFile: './crawler.log'
};

// 日志函数
const log = (message, level = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  console.log(logMessage);
  
  // 异步写入日志文件
  fs.appendFile(CONFIG.logFile, logMessage + '\n').catch(err => {
    console.error('写入日志失败:', err.message);
  });
};

// 安全执行函数
const safeExecute = async (fn, fallback = null, context = '') => {
  try {
    return await fn();
  } catch (error) {
    log(`${context} 执行失败: ${error.message}`, 'ERROR');
    return fallback;
  }
};

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 获取默认图片
const getDefaultImage = (filename) => {
  const defaultImages = {
    'news.json': 'https://images.unsplash.com/photo-1551190822-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'tcm-science.json': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'therapy-methods.json': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  };
  return defaultImages[filename] || 'https://images.unsplash.com/photo-1551190822-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';
};

// 生成文章ID
const generateArticleId = (title, date) => {
  const hash = title.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  return Math.abs(hash) + Date.parse(date || new Date());
};

// 处理文章数据
const processArticle = (rawArticle, target) => {
  const now = new Date();
  const dateStr = rawArticle.date || now.toISOString().split('T')[0];

  return {
    id: generateArticleId(rawArticle.title, dateStr),
    title: rawArticle.title || '无标题',
    excerpt: rawArticle.excerpt || rawArticle.title?.substring(0, 100) + '...' || '暂无摘要',
    date: dateStr,
    category: target.category,
    categoryId: target.categoryId,
    image: rawArticle.image || getDefaultImage(target.filename),
    href: `/articles/${generateArticleId(rawArticle.title, dateStr)}`,
    tags: rawArticle.tags || [],
    views: Math.floor(Math.random() * 500) + 50,
    featured: false,
    wechatUrl: rawArticle.url || ''
  };
};

// 爬取搜狗微信搜索结果
const crawlSogouWechat = async (keyword) => {
  // 确保fetch已加载
  if (!fetch) {
    const { default: nodeFetch } = await import('node-fetch');
    fetch = nodeFetch;
  }

  const url = CONFIG.searchTemplate.replace('{keyword}', encodeURIComponent(keyword));
  log(`开始爬取: ${keyword} - ${url}`);

  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': CONFIG.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: CONFIG.timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    const $ = cheerio.load(html);
    const articles = [];

    // 解析搜索结果
    $('.news-box').each((index, element) => {
      if (index >= 10) return false; // 限制数量

      const $item = $(element);
      const title = $item.find('.news-tit a').text().trim();
      const url = $item.find('.news-tit a').attr('href');
      const excerpt = $item.find('.news-info').text().trim();
      const dateText = $item.find('.news-time').text().trim();

      if (title && url) {
        articles.push({
          title,
          url: url.startsWith('http') ? url : 'https://weixin.sogou.com' + url,
          excerpt: excerpt || title.substring(0, 100) + '...',
          date: parseDateFromText(dateText)
        });
      }
    });

    log(`成功爬取 ${keyword}: ${articles.length} 篇文章`);
    return articles;

  } catch (error) {
    log(`爬取 ${keyword} 失败: ${error.message}`, 'ERROR');
    return [];
  }
};

// 解析日期文本
const parseDateFromText = (dateText) => {
  if (!dateText) return new Date().toISOString().split('T')[0];

  const now = new Date();

  // 处理相对时间
  if (dateText.includes('今天')) {
    return now.toISOString().split('T')[0];
  } else if (dateText.includes('昨天')) {
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    return yesterday.toISOString().split('T')[0];
  } else if (dateText.includes('前天')) {
    const dayBeforeYesterday = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    return dayBeforeYesterday.toISOString().split('T')[0];
  }

  // 尝试解析具体日期
  const dateMatch = dateText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
  if (dateMatch) {
    return `${dateMatch[1]}-${dateMatch[2].padStart(2, '0')}-${dateMatch[3].padStart(2, '0')}`;
  }

  return now.toISOString().split('T')[0];
};

// 读取现有JSON文件
const readJsonFile = async (filePath) => {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    log(`读取文件失败 ${filePath}: ${error.message}`, 'WARN');
    return null;
  }
};

// 获取默认数据结构
const getDefaultDataStructure = (target) => {
  const categories = [
    { id: 'all', label: '全部', value: 'all' }
  ];

  // 根据不同文件添加特定分类
  if (target.filename === 'news.json') {
    categories.push(
      { id: 'hospital-news', label: '医院新闻', value: 'hospital-news' },
      { id: 'media-report', label: '媒体报道', value: 'media-report' },
      { id: 'academic', label: '学术交流', value: 'academic' }
    );
  } else if (target.filename === 'tcm-science.json') {
    categories.push(
      { id: 'basics', label: '中医基础', value: 'basics' },
      { id: 'health', label: '养生保健', value: 'health' },
      { id: 'herbs', label: '药材知识', value: 'herbs' },
      { id: 'treatment', label: '治疗方法', value: 'treatment' },
      { id: 'prevention', label: '疾病防治', value: 'prevention' }
    );
  } else if (target.filename === 'therapy-methods.json') {
    categories.push(
      { id: 'acupuncture', label: '针灸治疗', value: 'acupuncture' },
      { id: 'massage', label: '按摩治疗', value: 'massage' },
      { id: 'cupping', label: '拔罐治疗', value: 'cupping' },
      { id: 'others', label: '其他', value: 'others' }
    );
  }

  return {
    metadata: {
      lastUpdated: new Date().toISOString(),
      source: 'wechat-crawler',
      version: '1.0.0'
    },
    categories,
    articles: []
  };
};

// 更新JSON文件
const updateJsonFile = async (target, newArticles) => {
  const filePath = path.resolve(__dirname, CONFIG.dataDir, target.filename);

  try {
    // 确保目录存在
    await fs.mkdir(path.dirname(filePath), { recursive: true });

    // 读取现有数据
    let existingData = await readJsonFile(filePath);
    if (!existingData) {
      existingData = getDefaultDataStructure(target);
    }

    // 处理新文章
    const processedArticles = newArticles.map(article => processArticle(article, target));

    // 去重：基于标题去重
    const existingTitles = new Set(existingData.articles.map(a => a.title));
    const uniqueNewArticles = processedArticles.filter(article => !existingTitles.has(article.title));

    if (uniqueNewArticles.length === 0) {
      log(`${target.filename}: 没有新文章需要添加`);
      return;
    }

    // 合并数据：新文章在前，保留最新50篇
    const mergedArticles = [...uniqueNewArticles, ...existingData.articles].slice(0, 50);

    const updatedData = {
      ...existingData,
      metadata: {
        ...existingData.metadata,
        lastUpdated: new Date().toISOString()
      },
      articles: mergedArticles
    };

    // 写入文件
    await fs.writeFile(filePath, JSON.stringify(updatedData, null, 2), 'utf8');
    log(`✅ 成功更新 ${target.filename}: 添加 ${uniqueNewArticles.length} 篇新文章`);

  } catch (error) {
    log(`❌ 更新 ${target.filename} 失败: ${error.message}`, 'ERROR');
  }
};

// 主执行函数
const main = async () => {
  log('🚀 开始执行微信文章爬虫');

  const startTime = Date.now();
  let totalArticles = 0;
  let successCount = 0;

  for (const target of CONFIG.targets) {
    try {
      log(`📖 处理目标: ${target.keyword} -> ${target.filename}`);

      // 爬取文章
      const articles = await safeExecute(
        () => crawlSogouWechat(target.keyword),
        [],
        `爬取 ${target.keyword}`
      );

      if (articles.length > 0) {
        // 更新文件
        await safeExecute(
          () => updateJsonFile(target, articles),
          null,
          `更新 ${target.filename}`
        );

        totalArticles += articles.length;
        successCount++;
      } else {
        log(`⚠️  ${target.keyword} 没有获取到文章`, 'WARN');
      }

      // 请求间隔
      if (CONFIG.targets.indexOf(target) < CONFIG.targets.length - 1) {
        log(`⏳ 等待 ${CONFIG.requestDelay}ms...`);
        await delay(CONFIG.requestDelay);
      }

    } catch (error) {
      log(`❌ 处理 ${target.keyword} 时发生错误: ${error.message}`, 'ERROR');
    }
  }

  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);

  log(`✅ 爬虫执行完成！`);
  log(`📊 统计信息:`);
  log(`   - 处理目标: ${CONFIG.targets.length} 个`);
  log(`   - 成功处理: ${successCount} 个`);
  log(`   - 获取文章: ${totalArticles} 篇`);
  log(`   - 执行时间: ${duration} 秒`);

  // 保存执行记录到缓存
  const cacheDir = path.resolve(__dirname, CONFIG.cacheDir);
  await fs.mkdir(cacheDir, { recursive: true });

  const syncRecord = {
    timestamp: new Date().toISOString(),
    duration,
    targets: CONFIG.targets.length,
    success: successCount,
    totalArticles,
    details: CONFIG.targets.map(target => ({
      keyword: target.keyword,
      filename: target.filename
    }))
  };

  await safeExecute(
    () => fs.writeFile(
      path.join(cacheDir, 'last-sync.json'),
      JSON.stringify(syncRecord, null, 2)
    ),
    null,
    '保存同步记录'
  );
};

// 测试模式
const testMode = async () => {
  log('🧪 测试模式：只爬取第一个目标的少量数据');

  const target = CONFIG.targets[0];
  log(`测试目标: ${target.keyword}`);

  const articles = await crawlSogouWechat(target.keyword);
  log(`测试结果: 获取到 ${articles.length} 篇文章`);

  if (articles.length > 0) {
    log('文章示例:');
    articles.slice(0, 3).forEach((article, index) => {
      log(`  ${index + 1}. ${article.title}`);
      log(`     ${article.excerpt.substring(0, 50)}...`);
    });
  }
};

// 程序入口
if (require.main === module) {
  const isTest = process.argv.includes('--test');

  if (isTest) {
    testMode().catch(error => {
      log(`测试模式执行失败: ${error.message}`, 'ERROR');
      process.exit(1);
    });
  } else {
    main().catch(error => {
      log(`爬虫执行失败: ${error.message}`, 'ERROR');
      process.exit(1);
    });
  }
}

module.exports = {
  crawlSogouWechat,
  updateJsonFile,
  processArticle,
  main
};
