# 微信公众号RSS配置指南

## 📋 概述

本指南说明如何配置RSS源来获取特定微信公众号的文章。

## 🔧 RSS源配置方法

### 方法一：使用RSSHub（推荐）

RSSHub是一个开源的RSS生成器，支持微信公众号。

#### 配置步骤：

1. **获取公众号信息**
   - 公众号名称：`大连协和中西医结合医院`
   - 公众号ID（如果知道）：`dalianxiehe`

2. **构建RSS URL**
   ```
   https://rsshub.app/wechat/mp/大连协和中西医结合医院
   ```
   或者使用公众号ID：
   ```
   https://rsshub.app/wechat/mp/dalianxiehe
   ```

3. **在爬虫中配置**
   ```javascript
   {
     name: '大连协和中西医结合医院',
     rssUrl: 'https://rsshub.app/wechat/mp/大连协和中西医结合医院',
     filename: 'news.json',
     category: '医院新闻',
     categoryId: 'hospital-news',
     maxArticles: 30
   }
   ```

### 方法二：使用其他RSS服务

#### 可选的RSS服务：
- **RSSHub官方实例**: `https://rsshub.app/`
- **第三方实例**: `https://rss.lilydjwg.me/`
- **自建RSSHub**: 部署自己的RSSHub实例

#### 配置示例：
```javascript
fallbackRssUrls: [
  'https://rss.lilydjwg.me/wechat/大连协和中西医结合医院',
  'https://feeddd.org/feeds/wechat/大连协和中西医结合医院'
]
```

## 🔍 如何找到正确的RSS源

### 1. 验证公众号名称
- 在微信中搜索公众号
- 确认公众号的准确名称
- 记录公众号的微信号（如果有）

### 2. 测试RSS源
在浏览器中访问RSS URL，检查是否返回有效的XML：
```
https://rsshub.app/wechat/mp/大连协和中西医结合医院
```

### 3. 检查RSS格式
有效的RSS应该包含：
- `<rss>` 或 `<feed>` 根元素
- `<item>` 或 `<entry>` 文章条目
- `<title>`, `<link>`, `<description>` 等字段

## ⚙️ 爬虫配置文件

### 完整配置示例：

```javascript
const CONFIG = {
  targets: [
    {
      name: '大连协和中西医结合医院',
      rssUrl: 'https://rsshub.app/wechat/mp/大连协和中西医结合医院',
      filename: 'news.json',
      category: '医院新闻',
      categoryId: 'hospital-news',
      maxArticles: 30,
      fallbackRssUrls: [
        'https://rss.lilydjwg.me/wechat/大连协和中西医结合医院'
      ]
    }
  ],
  requestDelay: 3000,
  timeout: 15000
};
```

### 配置参数说明：

- **name**: 显示名称，用于日志
- **rssUrl**: 主要RSS源地址
- **filename**: 输出的JSON文件名
- **category**: 文章分类名称
- **categoryId**: 分类ID，用于前端筛选
- **maxArticles**: 最大文章数量
- **fallbackRssUrls**: 备用RSS源列表

## 🚨 常见问题

### 1. RSS源无法访问
**原因**: RSSHub实例可能不可用
**解决**: 
- 尝试其他RSSHub实例
- 检查网络连接
- 使用备用RSS源

### 2. 获取不到文章
**原因**: 公众号名称不正确或没有文章
**解决**:
- 确认公众号名称拼写
- 检查公众号是否有最近发布的文章
- 尝试使用公众号微信号

### 3. 文章内容不完整
**原因**: RSS只提供摘要
**解决**:
- 这是正常现象，RSS通常只包含文章摘要
- 如需完整内容，需要额外的内容抓取

## 🔧 高级配置

### 自定义RSS解析
如果需要解析特殊格式的RSS，可以修改`parseRSSFeed`函数：

```javascript
// 自定义字段提取
const customField = $item.find('custom:field').text();
```

### 多公众号聚合
配置多个公众号到同一个文件：

```javascript
{
  name: '医疗资讯聚合',
  rssUrl: 'https://rsshub.app/wechat/mp/医院1',
  filename: 'medical-news.json',
  fallbackRssUrls: [
    'https://rsshub.app/wechat/mp/医院2',
    'https://rsshub.app/wechat/mp/医院3'
  ]
}
```

## 📝 部署后配置

### 1. 修改配置文件
```bash
sudo nano /opt/wechat-crawler/crawler.js
```

### 2. 测试配置
```bash
cd /opt/wechat-crawler
node crawler.js --test
```

### 3. 查看日志
```bash
tail -f /var/log/wechat-crawler.log
```

## 🎯 最佳实践

1. **使用多个备用源**: 提高可用性
2. **合理设置延迟**: 避免被限制
3. **定期检查日志**: 及时发现问题
4. **测试RSS源**: 部署前验证可用性
5. **监控数据更新**: 确保内容及时更新

## 📞 技术支持

如果遇到配置问题，请检查：
1. RSS源是否可以在浏览器中正常访问
2. 公众号名称是否正确
3. 网络连接是否正常
4. 爬虫日志中的错误信息
