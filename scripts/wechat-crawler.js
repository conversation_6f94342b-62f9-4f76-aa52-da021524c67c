#!/usr/bin/env node

const cheerio = require('cheerio');
const fs = require('fs').promises;
const path = require('path');

// 动态导入node-fetch
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// 配置
const CONFIG = {
  // 目标公众号信息
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  
  // 输出配置
  outputDir: '../src/data/wechat',
  maxArticles: 50, // 最多保存文章数
  
  // 请求配置
  requestDelay: 2000, // 请求间隔(ms)
  timeout: 15000,
  
  // 用户代理
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
};

// 搜索微信公众号文章的方法
const searchMethods = [
  {
    name: '搜狗微信搜索',
    searchUrl: 'https://weixin.sogou.com/weixin?type=1&query=',
    parseFunction: parseSogouResults
  },
  {
    name: '微信公众号平台搜索',
    searchUrl: 'https://mp.weixin.qq.com/cgi-bin/searchbiz?action=search_biz&query=',
    parseFunction: parseWechatResults
  }
];

// 解析搜狗微信搜索结果
async function parseSogouResults(html, accountName) {
  const $ = cheerio.load(html);
  const articles = [];
  
  // 搜狗微信搜索结果解析
  $('.news-box').each((index, element) => {
    const $item = $(element);
    const title = $item.find('.news-tit a').text().trim();
    const link = $item.find('.news-tit a').attr('href');
    const summary = $item.find('.news-info').text().trim();
    const dateStr = $item.find('.news-time').text().trim();
    
    if (title && link) {
      articles.push({
        title,
        link: link.startsWith('http') ? link : `https://weixin.sogou.com${link}`,
        summary,
        publishDate: dateStr,
        source: '搜狗微信搜索'
      });
    }
  });
  
  return articles;
}

// 解析微信平台搜索结果
async function parseWechatResults(html, accountName) {
  const $ = cheerio.load(html);
  const articles = [];
  
  // 微信平台搜索结果解析
  $('.msg-card').each((index, element) => {
    const $item = $(element);
    const title = $item.find('.msg-card-title').text().trim();
    const link = $item.find('a').attr('href');
    const summary = $item.find('.msg-card-summary').text().trim();
    const dateStr = $item.find('.msg-card-time').text().trim();
    
    if (title && link) {
      articles.push({
        title,
        link: link.startsWith('http') ? link : `https://mp.weixin.qq.com${link}`,
        summary,
        publishDate: dateStr,
        source: '微信公众号平台'
      });
    }
  });
  
  return articles;
}

// 搜索公众号文章
async function searchWechatArticles(accountName) {
  console.log(`🔍 搜索公众号文章: ${accountName}`);
  
  // 确保fetch已加载
  if (!fetch) {
    const { default: nodeFetch } = await import('node-fetch');
    fetch = nodeFetch;
  }
  
  let allArticles = [];
  
  for (const method of searchMethods) {
    console.log(`\n📡 尝试方法: ${method.name}`);
    
    try {
      const searchUrl = `${method.searchUrl}${encodeURIComponent(accountName)}`;
      console.log(`🌐 请求URL: ${searchUrl}`);
      
      const response = await fetch(searchUrl, {
        headers: {
          'User-Agent': CONFIG.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        timeout: CONFIG.timeout
      });
      
      if (!response.ok) {
        console.log(`❌ 请求失败: ${response.status} ${response.statusText}`);
        continue;
      }
      
      const html = await response.text();
      console.log(`📄 获取内容长度: ${html.length} 字符`);
      
      const articles = await method.parseFunction(html, accountName);
      console.log(`📰 解析到文章: ${articles.length} 篇`);
      
      if (articles.length > 0) {
        allArticles = allArticles.concat(articles);
        console.log(`✅ ${method.name} 成功获取 ${articles.length} 篇文章`);
        
        // 显示示例文章
        articles.slice(0, 3).forEach((article, index) => {
          console.log(`   ${index + 1}. ${article.title}`);
        });
      }
      
      // 请求间隔
      await new Promise(resolve => setTimeout(resolve, CONFIG.requestDelay));
      
    } catch (error) {
      console.log(`❌ ${method.name} 失败: ${error.message}`);
    }
  }
  
  return allArticles;
}

// 手动添加已知文章
function addKnownArticles() {
  return [
    {
      title: '夏日吃瓜指南：一口清甜背后，这些细节你必须知道',
      link: 'https://mp.weixin.qq.com/s/i2Yz1mgQNM2Tb592UcpBMw',
      summary: '夏日吃瓜的健康指南和注意事项',
      publishDate: '昨天',
      source: '手动添加',
      category: 'health'
    },
    {
      title: '【科普】药食同源｜桑冬',
      link: '#',
      summary: '中医药食同源知识科普',
      publishDate: '阅读 13',
      source: '手动添加',
      category: 'education'
    },
    {
      title: '夏天吃剩饭剩菜，藏着多少健康隐患？医生提醒：这3类剩菜关于力别留',
      link: '#',
      summary: '夏季饮食安全提醒',
      publishDate: '阅读 61 赞 2',
      source: '手动添加',
      category: 'health'
    },
    {
      title: '【科普】药食同源｜地黄',
      link: '#',
      summary: '中医药食同源知识科普',
      publishDate: '阅读 1',
      source: '手动添加',
      category: 'education'
    },
    {
      title: '今年夏天，别再盲目喝绿豆汤了，正确搭配，才能解出健康！',
      link: '#',
      summary: '夏季养生饮食指导',
      publishDate: '星期四',
      source: '手动添加',
      category: 'health'
    }
  ];
}

// 处理和清理文章数据
function processArticles(articles) {
  // 去重
  const uniqueArticles = articles.filter((article, index, self) => 
    index === self.findIndex(a => a.title === article.title)
  );
  
  // 添加ID和分类
  const processedArticles = uniqueArticles.map((article, index) => ({
    id: `wechat_${Date.now()}_${index}`,
    title: article.title,
    link: article.link,
    summary: article.summary || '',
    publishDate: article.publishDate || '',
    source: article.source || '微信公众号',
    category: article.category || categorizeArticle(article.title),
    crawlTime: new Date().toISOString(),
    account: CONFIG.wechatAccount.name
  }));
  
  // 限制数量
  return processedArticles.slice(0, CONFIG.maxArticles);
}

// 根据标题自动分类
function categorizeArticle(title) {
  if (title.includes('科普') || title.includes('药食同源') || title.includes('中医')) {
    return 'education';
  } else if (title.includes('健康') || title.includes('养生') || title.includes('饮食')) {
    return 'health';
  } else if (title.includes('医院') || title.includes('科室') || title.includes('专家')) {
    return 'hospital';
  } else {
    return 'news';
  }
}

// 保存文章数据
async function saveArticles(articles) {
  try {
    // 确保输出目录存在
    const outputPath = path.resolve(__dirname, CONFIG.outputDir);
    await fs.mkdir(outputPath, { recursive: true });
    
    // 保存到不同文件
    const categorizedArticles = {
      all: articles,
      news: articles.filter(a => a.category === 'news'),
      health: articles.filter(a => a.category === 'health'),
      education: articles.filter(a => a.category === 'education'),
      hospital: articles.filter(a => a.category === 'hospital')
    };
    
    for (const [category, categoryArticles] of Object.entries(categorizedArticles)) {
      const filename = `wechat-${category}.json`;
      const filepath = path.join(outputPath, filename);
      
      const data = {
        updateTime: new Date().toISOString(),
        account: CONFIG.wechatAccount,
        category: category,
        total: categoryArticles.length,
        articles: categoryArticles
      };
      
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      console.log(`💾 保存 ${category} 类文章: ${categoryArticles.length} 篇 -> ${filename}`);
    }
    
    console.log(`✅ 文章数据保存完成，输出目录: ${outputPath}`);
    
  } catch (error) {
    console.error(`❌ 保存文章失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始爬取微信公众号文章');
  console.log(`📱 目标账号: ${CONFIG.wechatAccount.name} (${CONFIG.wechatAccount.wechatId})`);
  console.log('=' .repeat(60));
  
  try {
    // 搜索文章
    let articles = await searchWechatArticles(CONFIG.wechatAccount.name);
    
    // 如果搜索失败，使用手动添加的文章
    if (articles.length === 0) {
      console.log('\n⚠️  搜索未找到文章，使用手动添加的文章数据');
      articles = addKnownArticles();
    }
    
    console.log(`\n📊 总共获取文章: ${articles.length} 篇`);
    
    // 处理文章数据
    const processedArticles = processArticles(articles);
    console.log(`📝 处理后文章: ${processedArticles.length} 篇`);
    
    // 保存文章
    await saveArticles(processedArticles);
    
    console.log('\n🎉 爬取任务完成！');
    
  } catch (error) {
    console.error(`❌ 爬取失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行爬虫
if (require.main === module) {
  main();
}

module.exports = { 
  searchWechatArticles, 
  processArticles, 
  saveArticles, 
  CONFIG 
};
