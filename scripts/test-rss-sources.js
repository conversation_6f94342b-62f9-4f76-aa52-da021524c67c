#!/usr/bin/env node

const cheerio = require('cheerio');
const fs = require('fs').promises;

// 动态导入node-fetch
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// 测试的RSS源列表
const RSS_SOURCES = [
  {
    name: 'RSSHub官方 - 公众号名称',
    url: 'https://rsshub.app/wechat/mp/大连协和中西医结合医院'
  },
  {
    name: 'RSSHub官方 - 微信号',
    url: 'https://rsshub.app/wechat/mp/dl-xiehe'
  },
  {
    name: 'RSSHub官方 - 账号主体',
    url: 'https://rsshub.app/wechat/mp/大连东海医院'
  },
  {
    name: 'RSSHub镜像1 - 公众号名称',
    url: 'https://rss.lilydjwg.me/wechat/大连协和中西医结合医院'
  },
  {
    name: 'RSSHub镜像1 - 微信号',
    url: 'https://rss.lilydjwg.me/wechat/dl-xiehe'
  },
  {
    name: 'RSSHub镜像2 - 公众号名称',
    url: 'https://feeddd.org/feeds/wechat/大连协和中西医结合医院'
  },
  {
    name: 'RSSHub镜像2 - 微信号',
    url: 'https://feeddd.org/feeds/wechat/dl-xiehe'
  },
  {
    name: 'WeRSS服务',
    url: 'https://werss.app/api/v1.0/feeds/wechat/dl-xiehe'
  }
];

// 测试单个RSS源
const testRSSSource = async (source) => {
  console.log(`\n🔍 测试: ${source.name}`);
  console.log(`📡 URL: ${source.url}`);
  
  try {
    // 确保fetch已加载
    if (!fetch) {
      const { default: nodeFetch } = await import('node-fetch');
      fetch = nodeFetch;
    }
    
    const response = await fetch(source.url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/rss+xml, application/xml, text/xml, */*'
      },
      timeout: 15000
    });
    
    console.log(`📊 状态码: ${response.status}`);
    
    if (!response.ok) {
      console.log(`❌ 请求失败: ${response.status} ${response.statusText}`);
      return { success: false, error: `HTTP ${response.status}` };
    }
    
    const content = await response.text();
    console.log(`📄 内容长度: ${content.length} 字符`);
    
    // 检查是否是有效的XML
    if (!content.includes('<') || (!content.includes('<rss') && !content.includes('<feed'))) {
      console.log(`❌ 不是有效的RSS/XML格式`);
      return { success: false, error: '非XML格式' };
    }
    
    // 解析XML
    const $ = cheerio.load(content, { xmlMode: true });
    
    let articles = [];
    let format = '';
    
    // 检查RSS格式
    if ($('rss').length > 0) {
      format = 'RSS';
      $('item').each((index, element) => {
        if (index >= 5) return false; // 只检查前5篇
        
        const $item = $(element);
        const title = $item.find('title').text().trim();
        const link = $item.find('link').text().trim();
        const pubDate = $item.find('pubDate').text().trim();
        
        if (title) {
          articles.push({ title, link, pubDate });
        }
      });
    }
    // 检查Atom格式
    else if ($('feed').length > 0) {
      format = 'Atom';
      $('entry').each((index, element) => {
        if (index >= 5) return false; // 只检查前5篇
        
        const $item = $(element);
        const title = $item.find('title').text().trim();
        const link = $item.find('link').attr('href') || $item.find('link').text().trim();
        const published = $item.find('published').text().trim() || $item.find('updated').text().trim();
        
        if (title) {
          articles.push({ title, link, pubDate: published });
        }
      });
    }
    
    console.log(`📋 格式: ${format}`);
    console.log(`📰 找到文章: ${articles.length} 篇`);
    
    if (articles.length > 0) {
      console.log(`✅ 成功获取文章！`);
      console.log(`📝 最新文章示例:`);
      articles.slice(0, 3).forEach((article, index) => {
        console.log(`   ${index + 1}. ${article.title}`);
        console.log(`      链接: ${article.link}`);
        console.log(`      时间: ${article.pubDate}`);
      });
      
      return { 
        success: true, 
        format, 
        articles: articles.length,
        sampleArticles: articles.slice(0, 3)
      };
    } else {
      console.log(`⚠️  RSS源可访问但没有找到文章`);
      return { success: false, error: '无文章内容' };
    }
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
};

// 主测试函数
const main = async () => {
  console.log('🚀 开始测试微信公众号RSS源');
  console.log('📱 目标公众号: 大连协和中西医结合医院 (dl-xiehe)');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const source of RSS_SOURCES) {
    const result = await testRSSSource(source);
    results.push({
      name: source.name,
      url: source.url,
      ...result
    });
    
    // 请求间隔
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果汇总:');
  console.log('='.repeat(60));
  
  const successfulSources = results.filter(r => r.success);
  const failedSources = results.filter(r => !r.success);
  
  if (successfulSources.length > 0) {
    console.log(`\n✅ 可用的RSS源 (${successfulSources.length}个):`);
    successfulSources.forEach((source, index) => {
      console.log(`${index + 1}. ${source.name}`);
      console.log(`   URL: ${source.url}`);
      console.log(`   格式: ${source.format}, 文章数: ${source.articles}`);
    });
    
    console.log(`\n🎯 推荐配置:`);
    const bestSource = successfulSources[0];
    console.log(`主RSS源: ${bestSource.url}`);
    if (successfulSources.length > 1) {
      console.log(`备用源: ${successfulSources.slice(1).map(s => s.url).join(', ')}`);
    }
  } else {
    console.log(`\n❌ 没有找到可用的RSS源`);
  }
  
  if (failedSources.length > 0) {
    console.log(`\n❌ 不可用的RSS源 (${failedSources.length}个):`);
    failedSources.forEach((source, index) => {
      console.log(`${index + 1}. ${source.name} - ${source.error}`);
    });
  }
  
  // 保存结果到文件
  const reportData = {
    testTime: new Date().toISOString(),
    target: {
      name: '大连协和中西医结合医院',
      wechatId: 'dl-xiehe',
      entity: '大连东海医院'
    },
    results: results
  };
  
  await fs.writeFile('rss-test-report.json', JSON.stringify(reportData, null, 2));
  console.log(`\n📄 详细报告已保存到: rss-test-report.json`);
};

// 执行测试
if (require.main === module) {
  main().catch(error => {
    console.error(`测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testRSSSource, RSS_SOURCES };
