# 微信文章爬虫系统

轻量级微信公众号文章爬虫，自动获取文章并更新网站数据。

## 🎯 功能特点

- **轻量级**: 仅依赖2个npm包，总大小<3MB
- **自动化**: 每日定时执行，无需人工干预
- **智能去重**: 基于文章标题自动去重
- **错误处理**: 完善的错误处理和日志记录
- **数据降级**: 支持动态/静态数据自动切换

## 📁 文件结构

```
scripts/
├── crawler.js          # 主爬虫脚本
├── package.json        # 依赖配置
├── deploy-crawler.sh   # 部署脚本
└── README.md          # 说明文档
```

## 🚀 快速开始

### 1. 本地测试

```bash
# 进入脚本目录
cd scripts

# 安装依赖
pnpm install

# 测试运行
node crawler.js --test

# 正式运行
node crawler.js
```

### 2. 服务器部署

```bash
# 上传项目到服务器
scp -r scripts/ user@server:/path/to/project/

# 在服务器上运行部署脚本
sudo ./scripts/deploy-crawler.sh [安装目录] [网站数据目录]

# 示例
sudo ./scripts/deploy-crawler.sh /opt/wechat-crawler /var/www/html/src/data/dynamic
```

## ⚙️ 配置说明

### 爬虫配置

编辑 `crawler.js` 中的 `CONFIG` 对象：

```javascript
const CONFIG = {
  // 目标配置
  targets: [
    {
      keyword: '大连东海医院',      // 搜索关键词
      filename: 'news.json',       // 输出文件名
      category: '医院新闻',        // 分类名称
      categoryId: 'hospital-news', // 分类ID
      maxArticles: 20              // 最大文章数
    }
    // ... 更多配置
  ],
  
  // 请求设置
  requestDelay: 3000,  // 请求间隔(毫秒)
  timeout: 15000,      // 超时时间(毫秒)
  
  // 文件路径
  dataDir: '../src/data/dynamic',  // 数据输出目录
  logFile: './crawler.log'         // 日志文件
};
```

### 定时任务

默认每天凌晨2点执行：

```bash
# 查看定时任务
crontab -l

# 编辑定时任务
crontab -e

# 定时任务格式
0 2 * * * cd /opt/wechat-crawler && node crawler.js >> /var/log/wechat-crawler.log 2>&1
```

## 📊 监控和维护

### 状态检查

```bash
# 运行状态检查脚本
/opt/wechat-crawler/check-status.sh

# 查看实时日志
tail -f /var/log/wechat-crawler.log

# 手动执行爬虫
/opt/wechat-crawler/run-crawler.sh
```

### 日志分析

```bash
# 查看最近的执行记录
tail -50 /var/log/wechat-crawler.log

# 查看错误日志
grep "ERROR" /var/log/wechat-crawler.log

# 查看成功记录
grep "成功" /var/log/wechat-crawler.log
```

## 🔧 故障排除

### 常见问题

1. **爬虫无法获取数据**
   - 检查网络连接
   - 确认搜索关键词正确
   - 查看是否被反爬虫限制

2. **文件写入失败**
   - 检查目录权限
   - 确认磁盘空间充足
   - 验证文件路径正确

3. **定时任务不执行**
   - 检查cron服务状态: `systemctl status cron`
   - 验证定时任务语法: `crontab -l`
   - 查看系统日志: `journalctl -u cron`

### 调试模式

```bash
# 启用详细日志
node crawler.js --verbose

# 测试单个目标
node crawler.js --test --target="大连东海医院"

# 不写入文件，只输出结果
node crawler.js --dry-run
```

## 📈 性能优化

### 建议配置

- **请求间隔**: 建议3-5秒，避免被限制
- **超时时间**: 建议15-30秒
- **最大文章数**: 建议20-50篇，保持数据新鲜

### 资源使用

- **内存使用**: 约50-100MB
- **CPU使用**: 执行时约10-20%
- **网络流量**: 每次约1-5MB

## 🛡️ 安全注意事项

1. **遵守robots.txt**: 尊重网站爬虫协议
2. **合理频率**: 避免过于频繁的请求
3. **内容使用**: 仅用于展示，注明来源
4. **数据保护**: 不存储敏感信息

## 📝 更新日志

### v1.0.0 (2024-12-15)
- 初始版本发布
- 支持搜狗微信搜索
- 自动数据去重和分类
- 完整的部署和监控脚本

## 🤝 技术支持

如遇问题，请检查：

1. 日志文件: `/var/log/wechat-crawler.log`
2. 数据文件: `/var/www/html/src/data/dynamic/`
3. 爬虫配置: `/opt/wechat-crawler/crawler.js`

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
