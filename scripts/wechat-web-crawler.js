#!/usr/bin/env node

/**
 * 微信公众号网页爬虫
 * 通过搜狗微信搜索获取公众号文章
 */

const cheerio = require('cheerio');
const fs = require('fs').promises;
const path = require('path');

// 动态导入node-fetch
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// 配置
const CONFIG = {
  // 目标公众号信息
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  
  // 搜索配置
  search: {
    // 搜狗微信搜索URL
    sogouUrl: 'https://weixin.sogou.com/weixin',
    // 搜索参数
    params: {
      type: 1, // 搜索公众号
      query: '大连协和中西医结合医院'
    },
    // 请求头
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    }
  },
  
  // 输出配置
  outputDir: '../src/data/wechat',
  maxArticles: 50,
  
  // 请求配置
  requestDelay: 3000, // 增加延迟避免被封
  timeout: 20000
};

// 搜索公众号
async function searchWechatAccount(accountName) {
  console.log(`🔍 搜索公众号: ${accountName}`);
  
  // 确保fetch已加载
  if (!fetch) {
    const { default: nodeFetch } = await import('node-fetch');
    fetch = nodeFetch;
  }
  
  const searchUrl = `${CONFIG.search.sogouUrl}?type=${CONFIG.search.params.type}&query=${encodeURIComponent(accountName)}`;
  console.log(`🌐 搜索URL: ${searchUrl}`);
  
  try {
    const response = await fetch(searchUrl, {
      headers: CONFIG.search.headers,
      timeout: CONFIG.timeout
    });
    
    if (!response.ok) {
      throw new Error(`搜索请求失败: ${response.status} ${response.statusText}`);
    }
    
    const html = await response.text();
    console.log(`📄 获取搜索结果: ${html.length} 字符`);
    
    return html;
    
  } catch (error) {
    console.error(`❌ 搜索失败: ${error.message}`);
    throw error;
  }
}

// 解析搜索结果，获取公众号链接
function parseAccountLink(html) {
  const $ = cheerio.load(html);
  
  // 查找公众号链接
  const accountLinks = [];
  
  $('.results .news-box').each((index, element) => {
    const $item = $(element);
    const title = $item.find('.news-tit a').text().trim();
    const link = $item.find('.news-tit a').attr('href');
    const description = $item.find('.news-info').text().trim();
    
    if (title && link && title.includes('大连协和')) {
      accountLinks.push({
        title,
        link: link.startsWith('http') ? link : `https://weixin.sogou.com${link}`,
        description
      });
    }
  });
  
  console.log(`🔗 找到公众号链接: ${accountLinks.length} 个`);
  return accountLinks;
}

// 获取公众号文章列表
async function getAccountArticles(accountLink) {
  console.log(`📰 获取公众号文章: ${accountLink}`);
  
  try {
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, CONFIG.requestDelay));
    
    const response = await fetch(accountLink, {
      headers: CONFIG.search.headers,
      timeout: CONFIG.timeout
    });
    
    if (!response.ok) {
      throw new Error(`获取文章列表失败: ${response.status}`);
    }
    
    const html = await response.text();
    console.log(`📄 获取文章页面: ${html.length} 字符`);
    
    return parseArticleList(html);
    
  } catch (error) {
    console.error(`❌ 获取文章失败: ${error.message}`);
    return [];
  }
}

// 解析文章列表
function parseArticleList(html) {
  const $ = cheerio.load(html);
  const articles = [];
  
  // 解析文章列表（根据实际页面结构调整选择器）
  $('.weui_media_box, .news-list li, .msg-card').each((index, element) => {
    const $item = $(element);
    
    // 尝试不同的选择器
    let title = $item.find('.weui_media_title, .news-tit, .msg-title').text().trim();
    let link = $item.find('a').attr('href');
    let summary = $item.find('.weui_media_desc, .news-info, .msg-desc').text().trim();
    let dateStr = $item.find('.weui_media_info, .news-time, .msg-time').text().trim();
    
    // 如果没有找到，尝试其他选择器
    if (!title) {
      title = $item.find('h3, h4, .title').text().trim();
    }
    
    if (!link) {
      link = $item.find('a').first().attr('href');
    }
    
    if (title && link && title.length > 5) {
      // 处理相对链接
      if (link && !link.startsWith('http')) {
        if (link.startsWith('//')) {
          link = `https:${link}`;
        } else if (link.startsWith('/')) {
          link = `https://mp.weixin.qq.com${link}`;
        }
      }
      
      articles.push({
        id: `wechat_web_${Date.now()}_${index}`,
        title,
        link,
        summary: summary || '',
        publishDate: dateStr || '最近',
        source: '网页爬取',
        category: categorizeArticle(title),
        crawlTime: new Date().toISOString(),
        account: CONFIG.wechatAccount.name
      });
    }
  });
  
  console.log(`📊 解析到文章: ${articles.length} 篇`);
  return articles;
}

// 根据标题自动分类
function categorizeArticle(title) {
  if (title.includes('科普') || title.includes('药食同源') || title.includes('中医')) {
    return 'education';
  } else if (title.includes('健康') || title.includes('养生') || title.includes('饮食')) {
    return 'health';
  } else if (title.includes('医院') || title.includes('科室') || title.includes('专家')) {
    return 'hospital';
  } else {
    return 'news';
  }
}

// 保存文章数据
async function saveArticles(articles) {
  try {
    // 确保输出目录存在
    const outputPath = path.resolve(__dirname, CONFIG.outputDir);
    await fs.mkdir(outputPath, { recursive: true });
    
    // 保存到不同文件
    const categorizedArticles = {
      all: articles,
      news: articles.filter(a => a.category === 'news'),
      health: articles.filter(a => a.category === 'health'),
      education: articles.filter(a => a.category === 'education'),
      hospital: articles.filter(a => a.category === 'hospital')
    };
    
    for (const [category, categoryArticles] of Object.entries(categorizedArticles)) {
      const filename = `wechat-${category}.json`;
      const filepath = path.join(outputPath, filename);
      
      const data = {
        updateTime: new Date().toISOString(),
        account: CONFIG.wechatAccount,
        category: category,
        total: categoryArticles.length,
        articles: categoryArticles
      };
      
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      console.log(`💾 保存 ${category} 类文章: ${categoryArticles.length} 篇 -> ${filename}`);
    }
    
    console.log(`✅ 文章数据保存完成，输出目录: ${outputPath}`);
    
  } catch (error) {
    console.error(`❌ 保存文章失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始网页爬取微信公众号文章');
  console.log(`📱 目标账号: ${CONFIG.wechatAccount.name} (${CONFIG.wechatAccount.wechatId})`);
  console.log('=' .repeat(60));
  
  try {
    // 搜索公众号
    const searchHtml = await searchWechatAccount(CONFIG.wechatAccount.name);
    
    // 解析公众号链接
    const accountLinks = parseAccountLink(searchHtml);
    
    if (accountLinks.length === 0) {
      throw new Error('未找到目标公众号');
    }
    
    console.log(`✅ 找到公众号: ${accountLinks[0].title}`);
    
    // 获取文章列表
    const articles = await getAccountArticles(accountLinks[0].link);
    
    if (articles.length === 0) {
      throw new Error('未获取到文章列表');
    }
    
    // 限制文章数量
    const limitedArticles = articles.slice(0, CONFIG.maxArticles);
    
    console.log(`\n📊 总共获取文章: ${limitedArticles.length} 篇`);
    
    // 显示示例文章
    console.log('\n📝 文章示例:');
    limitedArticles.slice(0, 5).forEach((article, index) => {
      console.log(`${index + 1}. ${article.title}`);
      console.log(`   链接: ${article.link}`);
      console.log(`   分类: ${article.category}`);
    });
    
    // 保存文章
    await saveArticles(limitedArticles);
    
    console.log('\n🎉 网页爬取任务完成！');
    
  } catch (error) {
    console.error(`❌ 网页爬取失败: ${error.message}`);
    
    console.log('\n💡 建议:');
    console.log('1. 检查网络连接');
    console.log('2. 尝试更换搜索关键词');
    console.log('3. 检查搜狗微信搜索是否可访问');
    console.log('4. 考虑使用代理或VPN');
    
    process.exit(1);
  }
}

// 执行爬虫
if (require.main === module) {
  main();
}

module.exports = { 
  searchWechatAccount,
  parseAccountLink,
  getAccountArticles,
  parseArticleList,
  saveArticles,
  CONFIG 
};
