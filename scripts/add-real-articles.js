#!/usr/bin/env node

/**
 * 添加真实的微信公众号文章
 * 基于用户提供的真实文章信息
 */

const fs = require('fs').promises;
const path = require('path');

// 配置
const CONFIG = {
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  outputDir: '../src/data/wechat'
};

// 真实文章数据（基于用户提供的信息）
const REAL_ARTICLES = [
  {
    title: '夏日吃瓜指南：一口清甜背后，这些细节你必须知道',
    link: 'https://mp.weixin.qq.com/s/i2Yz1mgQNM2Tb592UcpBMw',
    summary: '夏季吃瓜的健康指南，包括选瓜技巧、食用注意事项等实用知识',
    publishDate: '2025-07-14',
    category: 'health'
  }
  // 注意：这里只添加了用户提供的真实文章
  // 其他文章需要通过手动导入工具或API获取
];

// 根据标题自动分类
function categorizeArticle(title) {
  if (title.includes('科普') || title.includes('药食同源') || title.includes('中医')) {
    return 'education';
  } else if (title.includes('健康') || title.includes('养生') || title.includes('饮食') || title.includes('吃瓜')) {
    return 'health';
  } else if (title.includes('医院') || title.includes('科室') || title.includes('专家')) {
    return 'hospital';
  } else {
    return 'news';
  }
}

// 处理文章数据
function processArticles(rawArticles) {
  return rawArticles.map((article, index) => ({
    id: `wechat_real_${Date.now()}_${index}`,
    title: article.title,
    link: article.link,
    summary: article.summary || '',
    publishDate: article.publishDate,
    source: '真实文章',
    category: article.category || categorizeArticle(article.title),
    crawlTime: new Date().toISOString(),
    account: CONFIG.wechatAccount.name
  }));
}

// 保存文章数据
async function saveArticles(articles) {
  try {
    // 确保输出目录存在
    const outputPath = path.resolve(__dirname, CONFIG.outputDir);
    await fs.mkdir(outputPath, { recursive: true });
    
    // 保存到不同文件
    const categorizedArticles = {
      all: articles,
      news: articles.filter(a => a.category === 'news'),
      health: articles.filter(a => a.category === 'health'),
      education: articles.filter(a => a.category === 'education'),
      hospital: articles.filter(a => a.category === 'hospital')
    };
    
    for (const [category, categoryArticles] of Object.entries(categorizedArticles)) {
      const filename = `wechat-${category}.json`;
      const filepath = path.join(outputPath, filename);
      
      const data = {
        updateTime: new Date().toISOString(),
        account: CONFIG.wechatAccount,
        category: category,
        total: categoryArticles.length,
        articles: categoryArticles,
        note: '基于真实微信公众号文章数据'
      };
      
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      console.log(`💾 保存 ${category} 类文章: ${categoryArticles.length} 篇 -> ${filename}`);
    }
    
    console.log(`✅ 真实文章数据保存完成，输出目录: ${outputPath}`);
    
  } catch (error) {
    console.error(`❌ 保存文章失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('🚀 添加真实微信公众号文章');
  console.log(`📱 目标账号: ${CONFIG.wechatAccount.name} (${CONFIG.wechatAccount.wechatId})`);
  console.log('=' .repeat(60));
  
  try {
    // 处理文章数据
    const processedArticles = processArticles(REAL_ARTICLES);
    
    console.log(`📊 处理文章数量: ${processedArticles.length} 篇`);
    
    // 显示文章信息
    console.log('\n📝 文章列表:');
    processedArticles.forEach((article, index) => {
      console.log(`${index + 1}. ${article.title}`);
      console.log(`   链接: ${article.link}`);
      console.log(`   分类: ${article.category}`);
      console.log(`   日期: ${article.publishDate}`);
    });
    
    // 保存文章
    await saveArticles(processedArticles);
    
    console.log('\n🎉 真实文章添加完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 使用手动导入工具添加更多文章: node wechat-manual-import.js');
    console.log('2. 或者配置微信API获取完整文章列表');
    console.log('3. 启动开发服务器查看效果: pnpm dev');
    
  } catch (error) {
    console.error(`❌ 添加失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  REAL_ARTICLES,
  processArticles,
  saveArticles,
  CONFIG 
};
