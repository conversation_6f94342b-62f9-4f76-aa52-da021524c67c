# 微信公众号文章自动更新 - Crontab 配置示例
# 
# 使用说明:
# 1. 复制下面的配置到服务器的 crontab 中
# 2. 修改路径为实际的项目路径
# 3. 确保脚本有执行权限: chmod +x /path/to/update-wechat-articles.sh
# 
# 安装 crontab:
# crontab -e
# 
# 查看当前 crontab:
# crontab -l
# 
# 删除 crontab:
# crontab -r

# ============================================================================
# 微信公众号文章自动更新任务
# ============================================================================

# 每天早上 8:00 更新一次
0 8 * * * cd /path/to/your/project/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 每天下午 18:00 更新一次
0 18 * * * cd /path/to/your/project/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 每隔 6 小时更新一次（可选，更频繁的更新）
# 0 */6 * * * cd /path/to/your/project/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 每周一凌晨 2:00 清理旧日志（可选）
# 0 2 * * 1 find /var/log -name "wechat-update.log*" -mtime +30 -delete

# ============================================================================
# 时间格式说明
# ============================================================================
# 
# 分钟 小时 日 月 星期 命令
# 
# 分钟: 0-59
# 小时: 0-23
# 日: 1-31
# 月: 1-12
# 星期: 0-7 (0和7都表示星期日)
# 
# 特殊字符:
# * : 任意值
# , : 列举多个值 (如: 1,3,5)
# - : 范围 (如: 1-5)
# / : 间隔 (如: */2 表示每2个单位)
# 
# 示例:
# 0 8 * * * : 每天早上8点
# 0 */6 * * * : 每6小时
# 0 8 * * 1 : 每周一早上8点
# 0 8 1 * * : 每月1号早上8点

# ============================================================================
# 生产环境配置建议
# ============================================================================

# 1. 标准配置 - 每天两次更新
# 适合大多数情况，平衡更新频率和服务器负载
0 8 * * * cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1
0 18 * * * cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 2. 高频配置 - 每4小时更新一次
# 适合需要及时更新的场景
# 0 */4 * * * cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 3. 低频配置 - 每天一次
# 适合更新不频繁的场景
# 0 8 * * * cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# 4. 工作日配置 - 仅工作日更新
# 适合医院工作时间的更新需求
# 0 8 * * 1-5 cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1
# 0 18 * * 1-5 cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh >> /var/log/wechat-update.log 2>&1

# ============================================================================
# 日志管理
# ============================================================================

# 每周清理超过30天的日志文件
0 2 * * 1 find /var/log -name "wechat-update.log*" -mtime +30 -delete

# 每天轮转日志文件（可选）
# 0 0 * * * mv /var/log/wechat-update.log /var/log/wechat-update.log.$(date +\%Y\%m\%d) && touch /var/log/wechat-update.log

# ============================================================================
# 监控和通知（可选）
# ============================================================================

# 每天检查更新状态并发送报告
# 0 9 * * * cd /var/www/hospital-website/scripts && ./check-wechat-update-status.sh

# 如果更新失败，发送邮件通知（需要配置邮件服务）
# 0 */6 * * * cd /var/www/hospital-website/scripts && ./update-wechat-articles.sh || echo "微信文章更新失败" | mail -s "更新失败通知" <EMAIL>

# ============================================================================
# 环境变量设置
# ============================================================================

# 如果需要特定的环境变量，可以在 crontab 顶部设置
# PATH=/usr/local/bin:/usr/bin:/bin
# NODE_ENV=production
# TZ=Asia/Shanghai

# ============================================================================
# 安装和测试步骤
# ============================================================================

# 1. 测试脚本是否正常工作
# cd /path/to/your/project/scripts
# ./update-wechat-articles.sh

# 2. 添加到 crontab
# crontab -e
# 粘贴上面的配置行

# 3. 验证 crontab 是否正确设置
# crontab -l

# 4. 检查日志文件
# tail -f /var/log/wechat-update.log

# 5. 手动测试 cron 任务
# sudo service cron restart  # 重启 cron 服务
# sudo service cron status   # 检查 cron 服务状态

# ============================================================================
# 故障排除
# ============================================================================

# 1. 检查脚本权限
# ls -la /path/to/your/project/scripts/update-wechat-articles.sh
# chmod +x /path/to/your/project/scripts/update-wechat-articles.sh

# 2. 检查路径是否正确
# which node
# which pnpm

# 3. 检查 cron 服务是否运行
# sudo service cron status

# 4. 查看 cron 日志
# sudo tail -f /var/log/syslog | grep CRON

# 5. 测试环境变量
# env | grep PATH
# env | grep NODE

# ============================================================================
# 注意事项
# ============================================================================

# 1. 确保服务器时区设置正确
# 2. 定期检查日志文件大小，避免占用过多磁盘空间
# 3. 在生产环境中测试脚本的执行时间，避免在高峰期运行
# 4. 考虑设置脚本执行超时，避免长时间占用资源
# 5. 备份重要数据，确保更新过程不会丢失数据
