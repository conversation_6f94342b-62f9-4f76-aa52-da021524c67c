{"testTime": "2025-07-15T17:29:58.637Z", "target": {"name": "大连协和中西医结合医院", "wechatId": "dl-xiehe", "entity": "大连东海医院"}, "results": [{"name": "RSSHub官方 - 公众号名称", "url": "https://rsshub.app/wechat/mp/大连协和中西医结合医院", "success": false, "error": "request to https://rsshub.app/wechat/mp/%E5%A4%A7%E8%BF%9E%E5%8D%8F%E5%92%8C%E4%B8%AD%E8%A5%BF%E5%8C%BB%E7%BB%93%E5%90%88%E5%8C%BB%E9%99%A2 failed, reason: connect ETIMEDOUT 128.242.240.20:443"}, {"name": "RSSHub官方 - 微信号", "url": "https://rsshub.app/wechat/mp/dl-xiehe", "success": false, "error": "request to https://rsshub.app/wechat/mp/dl-xiehe failed, reason: connect ETIMEDOUT 128.242.240.20:443"}, {"name": "RSSHub官方 - 账号主体", "url": "https://rsshub.app/wechat/mp/大连东海医院", "success": false, "error": "request to https://rsshub.app/wechat/mp/%E5%A4%A7%E8%BF%9E%E4%B8%9C%E6%B5%B7%E5%8C%BB%E9%99%A2 failed, reason: connect ETIMEDOUT 128.242.240.20:443"}, {"name": "RSSHub镜像1 - 公众号名称", "url": "https://rss.lilydjwg.me/wechat/大连协和中西医结合医院", "success": false, "error": "HTTP 404"}, {"name": "RSSHub镜像1 - 微信号", "url": "https://rss.lilydjwg.me/wechat/dl-xiehe", "success": false, "error": "HTTP 404"}, {"name": "RSSHub镜像2 - 公众号名称", "url": "https://feeddd.org/feeds/wechat/大连协和中西医结合医院", "success": false, "error": "非XML格式"}, {"name": "RSSHub镜像2 - 微信号", "url": "https://feeddd.org/feeds/wechat/dl-xiehe", "success": false, "error": "非XML格式"}, {"name": "WeRSS服务", "url": "https://werss.app/api/v1.0/feeds/wechat/dl-xiehe", "success": false, "error": "非XML格式"}]}