#!/usr/bin/env node

/**
 * 微信公众号文章手动导入工具
 * 通过手动输入文章链接和信息来创建文章数据
 */

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

// 配置
const CONFIG = {
  // 目标公众号信息
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  
  // 输出配置
  outputDir: '../src/data/wechat'
};

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提问函数
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 分类选项
const CATEGORIES = {
  '1': { value: 'news', label: '医院新闻' },
  '2': { value: 'health', label: '健康科普' },
  '3': { value: 'education', label: '中医教育' },
  '4': { value: 'hospital', label: '医院介绍' }
};

// 显示分类选项
function showCategories() {
  console.log('\n📋 文章分类:');
  Object.entries(CATEGORIES).forEach(([key, cat]) => {
    console.log(`${key}. ${cat.label} (${cat.value})`);
  });
}

// 验证URL格式
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 自动分类建议
function suggestCategory(title) {
  if (title.includes('科普') || title.includes('药食同源') || title.includes('中医')) {
    return '3'; // education
  } else if (title.includes('健康') || title.includes('养生') || title.includes('饮食')) {
    return '2'; // health
  } else if (title.includes('医院') || title.includes('科室') || title.includes('专家')) {
    return '4'; // hospital
  } else {
    return '1'; // news
  }
}

// 输入单篇文章
async function inputArticle(index) {
  console.log(`\n📝 输入第 ${index + 1} 篇文章信息:`);
  console.log('=' .repeat(40));
  
  // 文章标题
  let title;
  while (!title) {
    title = await question('文章标题: ');
    if (!title.trim()) {
      console.log('❌ 标题不能为空，请重新输入');
      title = null;
    }
  }
  
  // 文章链接
  let link;
  while (!link) {
    link = await question('文章链接: ');
    if (!link.trim()) {
      console.log('❌ 链接不能为空，请重新输入');
      link = null;
    } else if (!isValidUrl(link)) {
      console.log('❌ 链接格式无效，请输入完整的URL');
      link = null;
    }
  }
  
  // 文章摘要
  const summary = await question('文章摘要 (可选): ');
  
  // 发布日期
  let publishDate = await question('发布日期 (格式: YYYY-MM-DD，留空使用今天): ');
  if (!publishDate.trim()) {
    publishDate = new Date().toLocaleDateString('zh-CN');
  }
  
  // 文章分类
  showCategories();
  const suggestedCat = suggestCategory(title);
  let categoryChoice;
  while (!categoryChoice) {
    const prompt = `选择分类 (1-4，建议: ${suggestedCat}): `;
    categoryChoice = await question(prompt);
    
    if (!categoryChoice.trim()) {
      categoryChoice = suggestedCat;
    }
    
    if (!CATEGORIES[categoryChoice]) {
      console.log('❌ 无效选择，请输入 1-4');
      categoryChoice = null;
    }
  }
  
  const category = CATEGORIES[categoryChoice];
  
  // 确认信息
  console.log('\n✅ 文章信息确认:');
  console.log(`标题: ${title}`);
  console.log(`链接: ${link}`);
  console.log(`摘要: ${summary || '无'}`);
  console.log(`日期: ${publishDate}`);
  console.log(`分类: ${category.label}`);
  
  const confirm = await question('\n确认添加这篇文章? (y/n): ');
  if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
    console.log('❌ 已取消添加');
    return null;
  }
  
  return {
    id: `wechat_manual_${Date.now()}_${index}`,
    title: title.trim(),
    link: link.trim(),
    summary: summary.trim() || '',
    publishDate: publishDate,
    source: '手动导入',
    category: category.value,
    crawlTime: new Date().toISOString(),
    account: CONFIG.wechatAccount.name
  };
}

// 批量输入文章
async function batchInputArticles() {
  const articles = [];
  
  console.log('🚀 微信公众号文章手动导入工具');
  console.log(`📱 目标账号: ${CONFIG.wechatAccount.name}`);
  console.log('=' .repeat(60));
  
  const countStr = await question('请输入要添加的文章数量: ');
  const count = parseInt(countStr);
  
  if (isNaN(count) || count <= 0) {
    console.log('❌ 无效的文章数量');
    return [];
  }
  
  for (let i = 0; i < count; i++) {
    const article = await inputArticle(i);
    if (article) {
      articles.push(article);
      console.log(`✅ 第 ${i + 1} 篇文章已添加`);
    }
    
    // 询问是否继续
    if (i < count - 1) {
      const continueInput = await question('\n继续输入下一篇? (y/n): ');
      if (continueInput.toLowerCase() !== 'y' && continueInput.toLowerCase() !== 'yes') {
        break;
      }
    }
  }
  
  return articles;
}

// 从文件导入
async function importFromFile() {
  const filePath = await question('请输入CSV文件路径: ');
  
  try {
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) {
      throw new Error('文件为空');
    }
    
    const articles = [];
    
    // 跳过标题行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      const columns = line.split(',').map(col => col.trim().replace(/"/g, ''));
      
      if (columns.length < 3) {
        console.log(`⚠️  跳过无效行 ${i + 1}: ${line}`);
        continue;
      }
      
      const [title, link, summary, publishDate, categoryNum] = columns;
      
      if (!title || !link) {
        console.log(`⚠️  跳过不完整行 ${i + 1}: ${line}`);
        continue;
      }
      
      const categoryChoice = categoryNum || suggestCategory(title);
      const category = CATEGORIES[categoryChoice] || CATEGORIES['1'];
      
      articles.push({
        id: `wechat_import_${Date.now()}_${i}`,
        title,
        link,
        summary: summary || '',
        publishDate: publishDate || new Date().toLocaleDateString('zh-CN'),
        source: '文件导入',
        category: category.value,
        crawlTime: new Date().toISOString(),
        account: CONFIG.wechatAccount.name
      });
    }
    
    console.log(`📊 从文件导入 ${articles.length} 篇文章`);
    return articles;
    
  } catch (error) {
    console.error(`❌ 文件导入失败: ${error.message}`);
    return [];
  }
}

// 保存文章数据
async function saveArticles(articles) {
  if (articles.length === 0) {
    console.log('⚠️  没有文章需要保存');
    return;
  }
  
  try {
    // 确保输出目录存在
    const outputPath = path.resolve(__dirname, CONFIG.outputDir);
    await fs.mkdir(outputPath, { recursive: true });
    
    // 读取现有数据
    let existingArticles = [];
    try {
      const existingData = await fs.readFile(path.join(outputPath, 'wechat-all.json'), 'utf8');
      const parsed = JSON.parse(existingData);
      existingArticles = parsed.articles || [];
    } catch (error) {
      console.log('📄 没有找到现有数据，将创建新文件');
    }
    
    // 合并文章（去重）
    const allArticles = [...existingArticles];
    articles.forEach(newArticle => {
      const exists = allArticles.some(existing => 
        existing.title === newArticle.title || existing.link === newArticle.link
      );
      if (!exists) {
        allArticles.push(newArticle);
      } else {
        console.log(`⚠️  跳过重复文章: ${newArticle.title}`);
      }
    });
    
    // 按时间排序
    allArticles.sort((a, b) => new Date(b.crawlTime).getTime() - new Date(a.crawlTime).getTime());
    
    // 保存到不同文件
    const categorizedArticles = {
      all: allArticles,
      news: allArticles.filter(a => a.category === 'news'),
      health: allArticles.filter(a => a.category === 'health'),
      education: allArticles.filter(a => a.category === 'education'),
      hospital: allArticles.filter(a => a.category === 'hospital')
    };
    
    for (const [category, categoryArticles] of Object.entries(categorizedArticles)) {
      const filename = `wechat-${category}.json`;
      const filepath = path.join(outputPath, filename);
      
      const data = {
        updateTime: new Date().toISOString(),
        account: CONFIG.wechatAccount,
        category: category,
        total: categoryArticles.length,
        articles: categoryArticles
      };
      
      await fs.writeFile(filepath, JSON.stringify(data, null, 2));
      console.log(`💾 保存 ${category} 类文章: ${categoryArticles.length} 篇 -> ${filename}`);
    }
    
    console.log(`✅ 文章数据保存完成，输出目录: ${outputPath}`);
    
  } catch (error) {
    console.error(`❌ 保存文章失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    console.log('📋 选择导入方式:');
    console.log('1. 手动逐篇输入');
    console.log('2. 从CSV文件导入');
    
    const choice = await question('请选择 (1-2): ');
    
    let articles = [];
    
    if (choice === '1') {
      articles = await batchInputArticles();
    } else if (choice === '2') {
      articles = await importFromFile();
    } else {
      console.log('❌ 无效选择');
      process.exit(1);
    }
    
    if (articles.length > 0) {
      await saveArticles(articles);
      console.log('\n🎉 导入任务完成！');
    } else {
      console.log('\n⚠️  没有文章被导入');
    }
    
  } catch (error) {
    console.error(`❌ 导入失败: ${error.message}`);
  } finally {
    rl.close();
  }
}

// 执行导入
if (require.main === module) {
  main();
}

module.exports = { 
  inputArticle,
  batchInputArticles,
  importFromFile,
  saveArticles,
  CONFIG 
};
