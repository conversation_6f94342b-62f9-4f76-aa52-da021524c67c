name: 更新微信公众号文章

on:
  # 定时执行 - 每天早上8点和下午6点
  schedule:
    - cron: '0 0,10 * * *'  # UTC时间，对应北京时间8:00和18:00
  
  # 手动触发
  workflow_dispatch:
    inputs:
      force_update:
        description: '强制更新所有文章'
        required: false
        default: 'false'
        type: boolean
  
  # 推送到main分支时触发（仅限scripts目录变更）
  push:
    branches: [ main ]
    paths:
      - 'scripts/wechat-crawler.js'
      - 'scripts/update-wechat-articles.sh'
      - '.github/workflows/update-wechat-articles.yml'

jobs:
  update-articles:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest
    
    - name: 安装依赖
      run: |
        cd scripts
        npm install cheerio node-fetch
    
    - name: 创建必要目录
      run: |
        mkdir -p src/data/wechat
        mkdir -p scripts/backups
    
    - name: 运行微信文章爬虫
      run: |
        cd scripts
        node wechat-crawler.js
      env:
        NODE_ENV: production
    
    - name: 验证生成的数据
      run: |
        # 检查文件是否存在
        if [ ! -f "src/data/wechat/wechat-all.json" ]; then
          echo "错误: 主数据文件不存在"
          exit 1
        fi
        
        # 检查JSON格式
        if ! node -e "JSON.parse(require('fs').readFileSync('src/data/wechat/wechat-all.json', 'utf8'))"; then
          echo "错误: JSON格式无效"
          exit 1
        fi
        
        # 显示文章统计
        echo "文章统计:"
        node -e "
          const data = JSON.parse(require('fs').readFileSync('src/data/wechat/wechat-all.json', 'utf8'));
          console.log('总文章数:', data.total);
          console.log('更新时间:', data.updateTime);
          console.log('账号信息:', data.account.name);
        "
    
    - name: 更新.gitignore
      run: |
        # 确保微信数据文件被忽略
        if ! grep -q "src/data/wechat/\*.json" .gitignore 2>/dev/null; then
          echo "" >> .gitignore
          echo "# 微信公众号文章数据 (动态生成)" >> .gitignore
          echo "src/data/wechat/*.json" >> .gitignore
          echo "已添加微信数据文件到.gitignore"
        fi
    
    - name: 生成更新报告
      run: |
        cat > scripts/wechat-update-report.json << EOF
        {
          "lastUpdate": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")",
          "workflow": "${{ github.workflow }}",
          "runId": "${{ github.run_id }}",
          "runNumber": "${{ github.run_number }}",
          "actor": "${{ github.actor }}",
          "event": "${{ github.event_name }}",
          "ref": "${{ github.ref }}",
          "sha": "${{ github.sha }}",
          "status": "success"
        }
        EOF
        
        echo "更新报告已生成"
        cat scripts/wechat-update-report.json
    
    - name: 提交更新的数据文件
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # 添加生成的文件
        git add src/data/wechat/*.json
        git add scripts/wechat-update-report.json
        git add .gitignore
        
        # 检查是否有变更
        if git diff --staged --quiet; then
          echo "没有文件变更，跳过提交"
        else
          git commit -m "🤖 自动更新微信公众号文章数据 $(date '+%Y-%m-%d %H:%M:%S')"
          git push
          echo "文章数据已更新并推送"
        fi
    
    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: wechat-articles-${{ github.run_number }}
        path: |
          src/data/wechat/
          scripts/wechat-update-report.json
          scripts/wechat-update.log
        retention-days: 30
    
    - name: 发送通知 (失败时)
      if: failure()
      run: |
        echo "微信文章更新失败"
        echo "工作流: ${{ github.workflow }}"
        echo "运行ID: ${{ github.run_id }}"
        echo "触发事件: ${{ github.event_name }}"
        echo "分支: ${{ github.ref }}"
        
        # 这里可以添加其他通知方式，如邮件、Slack等
    
    - name: 清理临时文件
      if: always()
      run: |
        # 清理可能的临时文件
        rm -f scripts/*.tmp
        rm -f scripts/*.log.tmp
        
        echo "清理完成"

  # 可选：部署到GitHub Pages（如果需要）
  deploy-to-pages:
    needs: update-articles
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name != 'schedule'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        ref: main  # 确保获取最新的提交
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装依赖
      run: pnpm install
    
    - name: 构建项目
      run: pnpm build
    
    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
        cname: your-domain.com  # 如果有自定义域名，请替换
