/**
 * 布局工具函数 - 用于TopNavigation组件的动态布局调整
 */

export interface LayoutConfig {
  logoMaxWidth: string;
  searchVisible: boolean;
  menuButtonText: boolean;
  containerPadding: string;
}

export interface TextMetrics {
  estimatedWidth: number;
  language: string;
  textLength: number;
}

/**
 * 估算文本宽度（基于字符数和语言特性）
 */
export const estimateTextWidth = (text: string, language: string): number => {
  if (!text) return 0;
  
  // 不同语言的字符宽度系数（相对于英文字符）
  const languageFactors = {
    zh: 1.8,  // 中文字符较宽
    en: 1.0,  // 英文基准
    ru: 1.1   // 俄文略宽于英文
  };
  
  const factor = languageFactors[language as keyof typeof languageFactors] || 1.0;
  const baseCharWidth = 8; // 基础字符宽度（像素）
  
  return text.length * baseCharWidth * factor;
};

/**
 * 获取文本度量信息
 */
export const getTextMetrics = (text: string, language: string): TextMetrics => {
  return {
    estimatedWidth: estimateTextWidth(text, language),
    language,
    textLength: text.length
  };
};

/**
 * 计算最优布局配置
 */
export const calculateOptimalLayout = (
  logoText: string,
  language: string,
  screenWidth: number
): LayoutConfig => {
  const logoMetrics = getTextMetrics(logoText, language);
  
  // 移动端断点
  const isMobile = screenWidth < 768;
  const isSmallMobile = screenWidth < 480;
  
  // 基础配置
  let config: LayoutConfig = {
    logoMaxWidth: 'auto',
    searchVisible: true,
    menuButtonText: true,
    containerPadding: 'px-3'
  };
  
  if (isMobile) {
    // 移动端优化 - 保持搜索按钮显示，调整布局
    const languageSwitcherWidth = 70; // 语言切换器宽度（缩放后）
    const menuButtonWidth = 32; // 菜单按钮最小宽度
    const searchButtonWidth = 32; // 搜索按钮宽度
    const paddingWidth = isSmallMobile ? 8 : 16; // 左右内边距
    const spacingWidth = 12; // 元素间距
    const reservedSpace = languageSwitcherWidth + menuButtonWidth + searchButtonWidth + paddingWidth + spacingWidth;
    const availableLogoSpace = screenWidth - reservedSpace;

    // 优先策略：保证logo显示，同时保留搜索功能
    if (logoMetrics.estimatedWidth <= availableLogoSpace) {
      // logo可以完整显示
      config.logoMaxWidth = 'auto';
      config.searchVisible = true; // 保持搜索按钮显示
      config.menuButtonText = false; // 移动端不显示菜单文本，节省空间
      config.containerPadding = isSmallMobile ? 'px-1' : 'px-2';
    } else {
      // logo无法完整显示，适当压缩logo但保留搜索功能
      const minReservedSpace = languageSwitcherWidth + menuButtonWidth + searchButtonWidth + 16; // 保留搜索按钮空间
      const maxLogoSpace = screenWidth - minReservedSpace;

      if (logoMetrics.estimatedWidth <= maxLogoSpace) {
        // 使用紧凑布局但保留搜索
        config.logoMaxWidth = 'auto';
        config.searchVisible = true;
        config.menuButtonText = false;
        config.containerPadding = 'px-1';
      } else {
        // 适度截断logo，但保留搜索功能
        const minLogoWidth = Math.max(maxLogoSpace * 0.7, 100); // 适当减少logo空间为搜索让路
        config.logoMaxWidth = `${minLogoWidth}px`;
        config.searchVisible = true;
        config.menuButtonText = false;
        config.containerPadding = 'px-1';
      }
    }
  } else {
    // 桌面端保持原有逻辑
    config.searchVisible = true;
    config.menuButtonText = true;
    config.logoMaxWidth = 'auto';
  }
  
  return config;
};

/**
 * 获取响应式CSS类
 */
export const getResponsiveClasses = (config: LayoutConfig) => {
  return {
    container: `flex items-center justify-between ${config.containerPadding} py-2 max-w-full`,
    logoContainer: 'flex items-center space-x-2 flex-shrink-0',
    logo: `text-red-600 font-bold text-lg ${
      config.logoMaxWidth !== 'auto' 
        ? `max-w-[${config.logoMaxWidth}] truncate` 
        : ''
    }`,
    rightSection: 'flex items-center space-x-2 flex-shrink-0',
    searchContainer: config.searchVisible 
      ? 'relative hidden md:block' 
      : 'relative hidden',
    mobileSearch: config.searchVisible 
      ? 'md:hidden p-2 text-gray-600 hover:bg-gray-100 rounded-md' 
      : 'hidden'
  };
};

/**
 * 检查是否需要应用紧凑布局
 */
export const shouldUseCompactLayout = (
  logoText: string,
  language: string,
  screenWidth: number
): boolean => {
  const logoMetrics = getTextMetrics(logoText, language);
  const isMobile = screenWidth < 768;

  return isMobile && logoMetrics.estimatedWidth > 150;
};

/**
 * 获取优化后的logo显示文本（在极端情况下可能需要缩写）
 */
export const getOptimizedLogoText = (
  originalText: string,
  language: string,
  maxWidth: string
): string => {
  if (maxWidth === 'auto') return originalText;

  // 如果设置了最大宽度，检查是否需要进一步优化
  const maxWidthNum = parseInt(maxWidth.replace('px', ''));
  const estimatedWidth = estimateTextWidth(originalText, language);

  if (estimatedWidth > maxWidthNum * 1.2) {
    // 如果文本仍然太长，考虑使用缩写
    switch (language) {
      case 'zh':
        // 中文可以保持原样，依赖CSS截断
        return originalText;
      case 'en':
        // 英文可以考虑缩写
        if (originalText.includes('Hospital')) {
          return originalText.replace('Hospital', 'Hosp.');
        }
        return originalText;
      case 'ru':
        // 俄文可以考虑缩写
        if (originalText.includes('Больница')) {
          return originalText.replace('Больница', 'Б-ца');
        }
        return originalText;
      default:
        return originalText;
    }
  }

  return originalText;
};
