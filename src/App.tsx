
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Layout from "./components/Layout";
import ScrollToTop from "./components/ScrollToTop";

// 基础页面
import Home from "./pages/Home";
import NotFound from "./pages/NotFound";

// 医院介绍模块
import HospitalIntroduction from "./pages/hospital/HospitalIntroduction";
import HospitalOverview from "./pages/hospital/HospitalOverview";
import HospitalCulture from "./pages/hospital/HospitalCulture";
import HospitalHistory from "./pages/hospital/HospitalHistory";
import SocialCooperation from "./pages/hospital/SocialCooperation";

// 新闻中心模块
import ArticleList from "./pages/news/ArticleList";
import ArticleDetail from "./pages/news/ArticleDetail";
import ArticleGallery from "./pages/news/ArticleGallery";
import HospitalStyle from "./pages/news/HospitalStyle";
import VideoDisplay from "./pages/news/VideoDisplay";

// 专家医生模块
import DoctorsList from "./pages/doctors/DoctorsList";
import DoctorDetail from "./pages/doctors/DoctorDetail";
import InternalMedicine from "./pages/doctors/departments/InternalMedicine";
import Surgery from "./pages/doctors/departments/Surgery";
import Dermatology from "./pages/doctors/departments/Dermatology";
import Gynecology from "./pages/doctors/departments/Gynecology";
import Pediatrics from "./pages/doctors/departments/Pediatrics";
import EntOphthalmology from "./pages/doctors/departments/EntOphthalmology";
import AcupunctureMassage from "./pages/doctors/departments/AcupunctureMassage";
import Ultrasound from "./pages/doctors/departments/Ultrasound";
import Proctology from "./pages/doctors/departments/Proctology";
import TcmRehabilitation from "./pages/doctors/departments/TcmRehabilitation";

// 健康管理模块
import HealthPackages from "./pages/health/HealthPackages";
import HealthCare from "./pages/health/HealthCare";

// 科普疗法模块
import TcmScience from "./pages/therapy/TcmScience";
import SpecialTherapy from "./pages/therapy/SpecialTherapy";

// 特殊页面模块
import ExportBase from "./pages/special/ExportBase";
import EducationBase from "./pages/special/EducationBase";

// 合作案例模块
import DomesticCooperation from "./pages/cooperation/DomesticCooperation";
import InternationalCooperation from "./pages/cooperation/InternationalCooperation";

// 联系我们模块
import ContactUs from "./pages/contact/ContactUs";
import EmailUs from "./pages/contact/EmailUs";

const queryClient = new QueryClient();  

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <ScrollToTop />
        <Layout>
          <Routes>
            {/* 首页 */}
            <Route path="/" element={<Home />} />

            {/* 医院介绍模块 */}
            <Route path="/hospital/overview" element={<HospitalOverview />} />
            <Route path="/hospital/culture" element={<HospitalCulture />} />
            <Route path="/hospital/history" element={<HospitalHistory />} />
            <Route path="/hospital/social-cooperation" element={<SocialCooperation />} />
            <Route path="/hospital" element={<HospitalIntroduction />} />

            {/* 新闻中心模块 */}
            <Route path="/articles" element={<ArticleList />} />
            <Route path="/news" element={<Navigate to="/articles" replace />} />
            <Route path="/article/:id" element={<ArticleDetail />} />
            <Route path="/gallery" element={<ArticleGallery />} />
            <Route path="/news/gallery" element={<HospitalStyle />} />
            <Route path="/news/videos" element={<VideoDisplay />} />

            {/* 专家医生模块 */}
            <Route path="/doctors" element={<DoctorsList />} />
            <Route path="/doctor/:id" element={<DoctorDetail />} />
            <Route path="/doctors/internal-medicine" element={<InternalMedicine />} />
            <Route path="/doctors/surgery" element={<Surgery />} />
            <Route path="/doctors/dermatology" element={<Dermatology />} />
            <Route path="/doctors/gynecology" element={<Gynecology />} />
            <Route path="/doctors/pediatrics" element={<Pediatrics />} />
            <Route path="/doctors/ent-ophthalmology" element={<EntOphthalmology />} />
            <Route path="/doctors/acupuncture-massage" element={<AcupunctureMassage />} />
            <Route path="/doctors/ultrasound" element={<Ultrasound />} />
            <Route path="/doctors/proctology" element={<Proctology />} />
            <Route path="/doctors/tcm-rehabilitation" element={<TcmRehabilitation />} />

            {/* 健康管理模块 */}
            <Route path="/health/packages" element={<HealthPackages />} />
            <Route path="/health/care" element={<HealthCare />} />

            {/* 科普疗法模块 */}
            <Route path="/therapy/tcm-science" element={<TcmScience />} />
            <Route path="/therapy/special" element={<SpecialTherapy />} />
            <Route path="/therapy/special-therapy" element={<SpecialTherapy />} />

            {/* 特殊页面模块 */}
            <Route path="/special/export-base" element={<ExportBase />} />
            <Route path="/special/education-base" element={<EducationBase />} />

            {/* 合作案例模块 */}
            <Route path="/cooperation" element={<DomesticCooperation />} />
            <Route path="/cooperation/domestic" element={<DomesticCooperation />} />
            <Route path="/cooperation/international" element={<InternationalCooperation />} />

            {/* 联系我们模块 */}
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/contact/info" element={<ContactUs />} />
            <Route path="/email" element={<EmailUs />} />
            <Route path="/contact/email" element={<Navigate to="/email" replace />} />

            {/* 404页面 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
