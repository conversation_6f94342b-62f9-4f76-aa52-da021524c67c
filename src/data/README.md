# 数据目录结构说明

本目录包含项目中使用的所有静态数据文件，采用分类管理的方式组织，便于维护和扩展。

## 目录结构

```
src/data/
├── README.md                 # 本说明文件
├── hospital/                 # 医院相关数据
│   ├── basic.json           # 医院基础信息
│   ├── overview.json        # 医院概况页面数据
│   └── culture.json         # 医院文化页面数据
├── articles/                # 文章相关数据
│   └── news.json           # 新闻文章数据
└── pages/                   # 页面内容数据
    └── content.json        # 通用页面内容
```

## 文件说明

### 医院数据 (`hospital/`)

#### `basic.json`
- **用途**: 存储医院基础信息和医院简介页面数据
- **包含内容**: 
  - 医院各部分介绍（概况、文化、历程、合作）
  - 医院基本信息（名称、地址、联系方式等）
  - 元数据信息
- **对应 Hook**: `useHospital`
- **使用页面**: `/hospital` (医院简介)

#### `overview.json`
- **用途**: 存储医院概况页面的所有数据
- **包含内容**:
  - 页面信息（标题、副标题等）
  - Hero 区域配置
  - 视频播放器配置
  - 医院介绍内容
  - 轮播图数据
- **对应 Hook**: `useHospitalOverview`
- **使用页面**: `/hospital/overview` (医院概况)

#### `culture.json`
- **用途**: 存储医院文化页面的所有数据
- **包含内容**:
  - 页面信息和 Hero 区域配置
  - 文化介绍内容
  - 核心价值观数据
  - 文化掠影图片和链接
- **对应 Hook**: `useHospitalCulture`
- **使用页面**: `/hospital/culture` (医院文化)

### 文章数据 (`articles/`)

#### `news.json`
- **用途**: 存储新闻文章数据
- **包含内容**: 文章列表、分类信息、标签等
- **对应 Hook**: `useArticles`
- **使用页面**: 文章列表、文章详情等

### 页面数据 (`pages/`)

#### `content.json`
- **用途**: 存储通用页面内容和配置
- **包含内容**: 页面元数据、通用文本内容等
- **使用范围**: 多个页面共享的内容

## 数据管理原则

### 1. 分类管理
- 按功能模块分类存储数据
- 每个模块有独立的目录
- 便于团队协作和维护

### 2. 命名规范
- 文件名使用小写字母和连字符
- 目录名使用复数形式
- 文件名要能清楚表达内容用途

### 3. 数据结构
- 每个 JSON 文件都包含 `metadata` 字段
- 使用一致的数据格式和字段命名
- 支持版本控制和变更追踪

### 4. 类型安全
- 每个数据文件都有对应的 TypeScript 接口
- 通过 Hook 提供类型安全的数据访问
- 统一的错误处理和加载状态管理

## 使用方式

### 1. 导入数据
```typescript
// 直接导入 JSON 文件
import hospitalData from '@/data/hospital/basic.json';

// 使用对应的 Hook（推荐）
import { useHospital } from '@/hooks/useHospital';
```

### 2. 在组件中使用
```typescript
const MyComponent = () => {
  const { hospitalInfo, hospitalSections, loading, error } = useHospital();
  
  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;
  
  return <div>{hospitalInfo.name}</div>;
};
```

## 扩展指南

### 添加新的数据文件
1. 在对应的分类目录下创建 JSON 文件
2. 定义对应的 TypeScript 接口
3. 创建或更新对应的 Hook
4. 更新本说明文件

### 修改现有数据
1. 直接编辑对应的 JSON 文件
2. 如果修改了数据结构，需要更新对应的接口
3. 测试相关组件是否正常工作

## 注意事项

1. **数据一致性**: 修改数据时要保持格式一致性
2. **版本控制**: 重要修改要更新 metadata 中的版本信息
3. **性能考虑**: 大型数据文件考虑分割或懒加载
4. **国际化**: 预留多语言支持的数据结构
