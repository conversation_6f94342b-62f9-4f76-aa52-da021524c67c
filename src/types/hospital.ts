// 医院相关类型定义

import { CarouselItem } from '@/components/Carousel';
import { VideoSource } from '@/components/VideoPlayer';

// 医院基础信息
export interface HospitalInfo {
  name: string;
  fullName: string;
  description: string;
  introduction: string;
  established: string;
  type: string;
  level: string;
  address: string;
  phone: string;
  website: string;
}

// 医院部分信息
export interface HospitalSection {
  id: number;
  title: string;
  excerpt: string;
  href: string;
  image: string;
  category: string;
  categoryId: string;
  tags: string[];
  featured: boolean;
  date: string;
  views: number;
  order?: number;
}

// 页面信息
export interface PageInfo {
  title: string;
  subtitle: string;
  description: string;
}

// Hero 区域配置
export interface HeroSection {
  title: string;
  description: string;
  backgroundImage: string;
  theme: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  height: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
}

// 医院名称配置
export interface HospitalName {
  name: string;
  showLines: boolean;
}

// 视频区域配置
export interface VideoSection {
  title: string;
  videoSources: VideoSource[];
  poster: string;
  aspectRatio: 'video' | 'square' | 'wide' | 'cinema';
  size: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

// 医院介绍配置
export interface HospitalIntroduction {
  title: string;
  image: string;
  content: string;
  readMoreText: string;
  readMoreHref: string;
}

// 其他内容标题配置
export interface OtherContentTitle {
  title: string;
  showLines: boolean;
}

// 轮播区域配置
export interface CarouselSection {
  title: string;
  autoPlay: boolean;
  autoPlayInterval: number;
  items: CarouselItem[];
}

// 元数据
export interface DataMetadata {
  version: string;
  lastUpdated: string;
  author: string;
  description: string;
}

// 医院基础数据结构
export interface HospitalBasicData {
  hospitalSections: HospitalSection[];
  hospitalInfo: HospitalInfo;
  metadata: DataMetadata;
}

// 医院概况数据结构
export interface HospitalOverviewData {
  pageInfo: PageInfo;
  heroSection: HeroSection;
  hospitalName: HospitalName;
  videoSection: VideoSection;
  hospitalIntroduction: HospitalIntroduction;
  otherContentTitle: OtherContentTitle;
  carouselSection: CarouselSection;
  metadata: DataMetadata;
}

// Hook 返回类型
export interface UseHospitalReturn {
  hospitalInfo: HospitalInfo;
  hospitalSections: HospitalSection[];
  loading: boolean;
  error: string | null;
  getHospitalSection: (id: string | number) => HospitalSection | undefined;
  getHospitalSectionsByCategory: (categoryId: string) => HospitalSection[];
  getFeaturedSections: () => HospitalSection[];
  metadata: DataMetadata;
}

export interface UseHospitalOverviewReturn {
  pageInfo: PageInfo;
  heroSection: HeroSection;
  hospitalName: HospitalName;
  videoSection: VideoSection;
  hospitalIntroduction: HospitalIntroduction;
  hospitalIntroductionArticle: any; // Article 类型
  otherContentTitle: OtherContentTitle;
  carouselSection: CarouselSection;
  loading: boolean;
  error: string | null;
  metadata: DataMetadata;
}

// 医院文化相关类型
export interface CultureIntroduction {
  title: string;
  image: string;
  content: string;
  readMoreText: string;
  readMoreHref: string;
}

export interface CoreValue {
  id: number;
  title: string;
  description: string;
  color: string;
}

export interface CoreValues {
  title: string;
  subtitle: string;
  values: CoreValue[];
}

export interface CulturalGalleryTitle {
  title: string;
  showLines: boolean;
}

export interface CulturalGalleryItem {
  id: number;
  title: string;
  subtitle: string;
  image: string;
  imageAlt: string;
  href: string;
}

// 医院文化数据结构
export interface HospitalCultureData {
  pageInfo: PageInfo;
  heroSection: HeroSection;
  cultureIntroduction: CultureIntroduction;
  coreValues: CoreValues;
  culturalGalleryTitle: CulturalGalleryTitle;
  culturalGallery: CulturalGalleryItem[];
  metadata: DataMetadata;
}

export interface UseHospitalCultureReturn {
  pageInfo: PageInfo;
  heroSection: HeroSection;
  cultureIntroduction: CultureIntroduction;
  cultureIntroductionArticle: any; // Article 类型
  coreValues: CoreValues;
  culturalGalleryTitle: CulturalGalleryTitle;
  culturalGallery: CulturalGalleryItem[];
  loading: boolean;
  error: string | null;
  metadata: DataMetadata;
}
