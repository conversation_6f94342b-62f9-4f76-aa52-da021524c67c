import { useState, useEffect, useMemo } from 'react';
import { WechatArticle } from '@/components/WechatArticlesList';

export interface WechatArticleData {
  updateTime: string;
  account: {
    name: string;
    wechatId: string;
    entity: string;
  };
  category: string;
  total: number;
  articles: WechatArticle[];
}

export interface UseWechatArticlesOptions {
  /** 分类筛选 */
  category?: 'all' | 'news' | 'health' | 'education' | 'hospital';
  /** 每页数量 */
  pageSize?: number;
  /** 当前页码 */
  page?: number;
  /** 搜索关键词 */
  searchKeyword?: string;
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 刷新间隔（毫秒） */
  refreshInterval?: number;
}

export interface UseWechatArticlesReturn {
  /** 文章列表 */
  articles: WechatArticle[];
  /** 总数量 */
  total: number;
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 最后更新时间 */
  lastUpdate: string | null;
  /** 公众号信息 */
  accountInfo: WechatArticleData['account'] | null;
  /** 刷新数据 */
  refresh: () => Promise<void>;
  /** 筛选文章 */
  filterArticles: (options: UseWechatArticlesOptions) => void;
  /** 获取单篇文章 */
  getArticleById: (id: string) => WechatArticle | undefined;
  /** 按分类获取文章统计 */
  getCategoryStats: () => Record<string, number>;
}

export const useWechatArticles = (
  initialOptions: UseWechatArticlesOptions = {}
): UseWechatArticlesReturn => {
  const [options, setOptions] = useState<UseWechatArticlesOptions>(initialOptions);
  const [allArticles, setAllArticles] = useState<WechatArticle[]>([]);
  const [accountInfo, setAccountInfo] = useState<WechatArticleData['account'] | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载微信文章数据
  const loadWechatArticles = async () => {
    try {
      setLoading(true);
      setError(null);

      // 根据分类选择数据文件
      const category = options.category || 'all';
      const filename = `wechat-${category}.json`;
      
      const response = await fetch(`/src/data/wechat/${filename}`);
      
      if (!response.ok) {
        throw new Error(`加载微信文章数据失败: ${response.status}`);
      }

      const data: WechatArticleData = await response.json();
      
      setAllArticles(data.articles || []);
      setAccountInfo(data.account);
      setLastUpdate(data.updateTime);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载微信文章数据失败';
      setError(errorMessage);
      console.error('加载微信文章数据失败:', err);
      
      // 如果加载失败，尝试加载备用数据
      if (options.category !== 'all') {
        try {
          const fallbackResponse = await fetch('/src/data/wechat/wechat-all.json');
          if (fallbackResponse.ok) {
            const fallbackData: WechatArticleData = await fallbackResponse.json();
            const filteredArticles = options.category 
              ? fallbackData.articles.filter(article => article.category === options.category)
              : fallbackData.articles;
            
            setAllArticles(filteredArticles);
            setAccountInfo(fallbackData.account);
            setLastUpdate(fallbackData.updateTime);
            setError(null);
          }
        } catch (fallbackErr) {
          console.error('加载备用数据也失败:', fallbackErr);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理和筛选文章
  const processedArticles = useMemo(() => {
    let filtered = [...allArticles];

    // 搜索筛选
    if (options.searchKeyword) {
      const keyword = options.searchKeyword.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(keyword) ||
        (article.summary && article.summary.toLowerCase().includes(keyword))
      );
    }

    // 排序（按爬取时间倒序）
    filtered.sort((a, b) => new Date(b.crawlTime).getTime() - new Date(a.crawlTime).getTime());

    // 分页
    if (options.pageSize && options.page) {
      const startIndex = (options.page - 1) * options.pageSize;
      const endIndex = startIndex + options.pageSize;
      filtered = filtered.slice(startIndex, endIndex);
    }

    return filtered;
  }, [allArticles, options]);

  // 刷新数据
  const refresh = async () => {
    await loadWechatArticles();
  };

  // 筛选文章
  const filterArticles = (newOptions: UseWechatArticlesOptions) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  };

  // 获取单篇文章
  const getArticleById = (id: string): WechatArticle | undefined => {
    return allArticles.find(article => article.id === id);
  };

  // 按分类获取文章统计
  const getCategoryStats = (): Record<string, number> => {
    const stats: Record<string, number> = {
      all: allArticles.length,
      news: 0,
      health: 0,
      education: 0,
      hospital: 0
    };

    allArticles.forEach(article => {
      if (stats[article.category] !== undefined) {
        stats[article.category]++;
      }
    });

    return stats;
  };

  // 初始加载
  useEffect(() => {
    loadWechatArticles();
  }, [options.category]);

  // 自动刷新
  useEffect(() => {
    if (!options.autoRefresh) return;

    const interval = setInterval(() => {
      loadWechatArticles();
    }, options.refreshInterval || 300000); // 默认5分钟

    return () => clearInterval(interval);
  }, [options.autoRefresh, options.refreshInterval]);

  return {
    articles: processedArticles,
    total: allArticles.length,
    loading,
    error,
    lastUpdate,
    accountInfo,
    refresh,
    filterArticles,
    getArticleById,
    getCategoryStats
  };
};

// 微信文章分类配置
export const WECHAT_CATEGORIES = [
  { id: 'all', label: '全部文章', value: 'all' },
  { id: 'news', label: '医院新闻', value: 'news' },
  { id: 'health', label: '健康科普', value: 'health' },
  { id: 'education', label: '中医教育', value: 'education' },
  { id: 'hospital', label: '医院介绍', value: 'hospital' }
];

// 获取分类标签
export const getCategoryLabel = (category: string): string => {
  const categoryItem = WECHAT_CATEGORIES.find(cat => cat.value === category);
  return categoryItem?.label || category;
};

// 格式化更新时间
export const formatUpdateTime = (updateTime: string): string => {
  try {
    const date = new Date(updateTime);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return '刚刚更新';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前更新`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前更新`;
    } else if (diffDays < 7) {
      return `${diffDays}天前更新`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  } catch (error) {
    return updateTime;
  }
};
