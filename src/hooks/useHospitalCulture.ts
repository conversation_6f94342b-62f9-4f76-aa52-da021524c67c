import { useState, useEffect, useMemo } from 'react';
import { Article } from './useArticles';
import hospitalCultureData from '@/data/hospital/culture.json';

// 页面信息接口
export interface PageInfo {
  title: string;
  subtitle: string;
  description: string;
}

// Hero 区域接口
export interface HeroSection {
  title: string;
  description: string;
  backgroundImage: string;
  theme: string;
  height: string;
}

// 文化介绍接口
export interface CultureIntroduction {
  title: string;
  image: string;
  content: string;
  readMoreText: string;
  readMoreHref: string;
}

// 核心价值观接口
export interface CoreValue {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  icon?: string;
  iconType?: string;
  color: string;
}

export interface CoreValues {
  title: string;
  subtitle: string;
  layout?: string;
  values: CoreValue[];
}

// 文化掠影标题接口
export interface CulturalGalleryTitle {
  title: string;
  showLines: boolean;
}

// 文化掠影项目接口
export interface CulturalGalleryItem {
  id: number;
  title: string;
  subtitle: string;
  image: string;
  imageAlt: string;
  href: string;
}

// 元数据接口
export interface DataMetadata {
  version: string;
  lastUpdated: string;
  author: string;
  description: string;
}

// 医院文化数据 Hook
export const useHospitalCulture = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 页面信息
  const pageInfo: PageInfo = useMemo(() => {
    return hospitalCultureData.pageInfo;
  }, []);

  // Hero 区域数据
  const heroSection: HeroSection = useMemo(() => {
    return hospitalCultureData.heroSection;
  }, []);

  // 文化介绍数据（转换为 Article 格式以兼容 ArticlesList）
  const cultureIntroductionArticle: Article = useMemo(() => {
    const intro = hospitalCultureData.cultureIntroduction;
    return {
      id: 'culture-introduction',
      title: intro.title,
      excerpt: intro.content, // 使用完整内容
      image: intro.image,
      href: intro.readMoreHref,
      category: '医院文化',
      categoryId: 'culture',
      tags: ['医院文化', '文化理念'],
      featured: true,
      date: new Date().toISOString().split('T')[0],
      views: 0
    };
  }, []);

  // 文化介绍原始数据
  const cultureIntroduction: CultureIntroduction = useMemo(() => {
    return hospitalCultureData.cultureIntroduction;
  }, []);

  // 核心价值观数据
  const coreValues: CoreValues = useMemo(() => {
    return hospitalCultureData.coreValues;
  }, []);

  // 文化掠影标题数据
  const culturalGalleryTitle: CulturalGalleryTitle = useMemo(() => {
    return hospitalCultureData.culturalGalleryTitle;
  }, []);

  // 文化掠影数据
  const culturalGallery: CulturalGalleryItem[] = useMemo(() => {
    return hospitalCultureData.culturalGallery;
  }, []);

  useEffect(() => {
    // 模拟数据加载
    const loadData = async () => {
      try {
        setLoading(true);
        // 这里可以添加实际的数据加载逻辑
        await new Promise(resolve => setTimeout(resolve, 100));
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载医院文化数据失败');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return {
    // 数据
    pageInfo,
    heroSection,
    cultureIntroduction,
    cultureIntroductionArticle,
    coreValues,
    culturalGalleryTitle,
    culturalGallery,
    
    // 状态
    loading,
    error,
    
    // 元数据
    metadata: hospitalCultureData.metadata
  };
};

export default useHospitalCulture;
