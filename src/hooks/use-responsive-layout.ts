import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { calculateOptimalLayout, LayoutConfig } from '@/utils/layoutUtils';

/**
 * 响应式布局Hook - 用于TopNavigation组件
 */
export const useResponsiveLayout = () => {
  const { t, i18n } = useTranslation();
  const [screenWidth, setScreenWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1024
  );

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 获取当前logo文本
  const logoText = t('common.logo.main');
  const currentLanguage = i18n.language;

  // 计算布局配置
  const layoutConfig: LayoutConfig = useMemo(() => {
    return calculateOptimalLayout(logoText, currentLanguage, screenWidth);
  }, [logoText, currentLanguage, screenWidth]);

  // 检查是否为移动端
  const isMobile = screenWidth < 768;
  const isSmallMobile = screenWidth < 480;

  // 获取动态样式
  const dynamicStyles = useMemo(() => {
    const logoMaxWidth = layoutConfig.logoMaxWidth;
    
    return {
      logoStyle: logoMaxWidth !== 'auto' ? {
        maxWidth: logoMaxWidth,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap' as const
      } : {},
      
      containerStyle: {
        padding: layoutConfig.containerPadding === 'px-2' ? '0.5rem' : '0.75rem'
      }
    };
  }, [layoutConfig]);

  return {
    layoutConfig,
    dynamicStyles,
    isMobile,
    isSmallMobile,
    screenWidth,
    logoText,
    currentLanguage
  };
};
