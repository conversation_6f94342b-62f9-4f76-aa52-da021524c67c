import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * 轻量化的页面标题管理Hook
 * @param title 页面标题，如果不传则只显示医院名称
 */
export const usePageTitle = (title?: string) => {
  const { t } = useTranslation();

  useEffect(() => {
    const hospitalName = t('common.logo.main', '大连东海医院');
    const pageTitle = title ? `${title} - ${hospitalName}` : hospitalName;
    document.title = pageTitle;
  }, [title, t]);
};
