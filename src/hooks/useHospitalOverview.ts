import { useState, useEffect, useMemo } from 'react';
import { Article } from './useArticles';
import { CarouselItem } from '@/components/Carousel';
import { VideoSource } from '@/components/VideoPlayer';
import hospitalOverviewData from '@/data/hospital/overview.json';

// 页面信息接口
export interface PageInfo {
  title: string;
  subtitle: string;
  description: string;
}

// Hero 区域接口
export interface HeroSection {
  title: string;
  description: string;
  backgroundImage: string;
  theme: string;
  height: string;
}

// 医院名称接口
export interface HospitalName {
  name: string;
  showLines: boolean;
}

// 视频区域接口
export interface VideoSection {
  title: string;
  videoSources: VideoSource[];
  poster: string;
  aspectRatio: string;
  size: string;
}

// 医院介绍接口
export interface HospitalIntroduction {
  title: string;
  image: string;
  content: string;
  readMoreText: string;
  readMoreHref: string;
}

// 其他内容标题接口
export interface OtherContentTitle {
  title: string;
  showLines: boolean;
}

// 轮播区域接口
export interface CarouselSection {
  title: string;
  autoPlay: boolean;
  autoPlayInterval: number;
  items: CarouselItem[];
}

// 医院概况数据 Hook
export const useHospitalOverview = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 页面信息
  const pageInfo: PageInfo = useMemo(() => {
    return hospitalOverviewData.pageInfo;
  }, []);

  // Hero 区域数据
  const heroSection: HeroSection = useMemo(() => {
    return hospitalOverviewData.heroSection;
  }, []);

  // 医院名称数据
  const hospitalName: HospitalName = useMemo(() => {
    return hospitalOverviewData.hospitalName;
  }, []);

  // 视频区域数据
  const videoSection: VideoSection = useMemo(() => {
    return hospitalOverviewData.videoSection;
  }, []);

  // 医院介绍数据（转换为 Article 格式以兼容 ArticlesList）
  const hospitalIntroductionArticle: Article = useMemo(() => {
    const intro = hospitalOverviewData.hospitalIntroduction;
    return {
      id: 'hospital-introduction',
      title: intro.title,
      excerpt: intro.content, // 使用完整内容而不是截断
      image: intro.image,
      href: intro.readMoreHref,
      category: '医院介绍',
      categoryId: 'introduction',
      tags: ['医院介绍', '概况'],
      featured: true,
      date: new Date().toISOString().split('T')[0],
      views: 0
    };
  }, []);

  // 医院介绍原始数据
  const hospitalIntroduction: HospitalIntroduction = useMemo(() => {
    return hospitalOverviewData.hospitalIntroduction;
  }, []);

  // 其他内容标题数据
  const otherContentTitle: OtherContentTitle = useMemo(() => {
    return hospitalOverviewData.otherContentTitle;
  }, []);

  // 轮播区域数据
  const carouselSection: CarouselSection = useMemo(() => {
    return hospitalOverviewData.carouselSection;
  }, []);

  useEffect(() => {
    // 模拟数据加载
    const loadData = async () => {
      try {
        setLoading(true);
        // 这里可以添加实际的数据加载逻辑
        await new Promise(resolve => setTimeout(resolve, 100));
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载医院概况数据失败');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return {
    // 数据
    pageInfo,
    heroSection,
    hospitalName,
    videoSection,
    hospitalIntroduction,
    hospitalIntroductionArticle,
    otherContentTitle,
    carouselSection,
    
    // 状态
    loading,
    error,
    
    // 元数据
    metadata: hospitalOverviewData.metadata
  };
};

export default useHospitalOverview;
