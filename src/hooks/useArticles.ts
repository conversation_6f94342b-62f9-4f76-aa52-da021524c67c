import { useState, useEffect, useMemo } from 'react';
import { CategoryItem } from '@/components/CategoryTabs';
import articlesData from '@/data/articles/news.json';

// 更新Article接口以匹配JSON数据结构
export interface Article {
  /** 文章ID */
  id: string | number;
  /** 文章标题 */
  title: string;
  /** 文章摘要 */
  excerpt?: string;
  /** 发布日期 */
  date: string;
  /** 浏览量 */
  views?: number;
  /** 分类 */
  category?: string;
  /** 分类ID */
  categoryId?: string;
  /** 封面图片 */
  image?: string;
  /** 作者 */
  author?: string;
  /** 文章链接 */
  href?: string;
  /** 标签 */
  tags?: string[];
  /** 是否为精选文章 */
  featured?: boolean;
  /** 自定义数据 */
  data?: any;
}

export interface UseArticlesOptions {
  /** 分类筛选 */
  categoryId?: string;
  /** 是否为精选文章 */
  featured?: boolean;
  /** 每页数量 */
  pageSize?: number;
  /** 当前页码 */
  page?: number;
  /** 搜索关键词 */
  searchKeyword?: string;
}

export interface UseArticlesReturn {
  /** 文章列表 */
  articles: Article[];
  /** 分类列表 */
  categories: CategoryItem[];
  /** 总数量 */
  total: number;
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 筛选文章 */
  filterArticles: (options: UseArticlesOptions) => void;
  /** 获取单篇文章 */
  getArticleById: (id: string | number) => Article | undefined;
  /** 获取相关文章 */
  getRelatedArticles: (articleId: string | number, limit?: number) => Article[];
}

export const useArticles = (initialOptions: UseArticlesOptions = {}): UseArticlesReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [options, setOptions] = useState<UseArticlesOptions>(initialOptions);

  // 模拟异步加载（实际项目中可能从API获取）
  useEffect(() => {
    setLoading(true);
    setError(null); // 重置错误状态

    try {
      // 模拟网络延迟
      const timer = setTimeout(() => {
        setLoading(false);
      }, 100);

      return () => clearTimeout(timer);
    } catch (err) {
      setError(err instanceof Error ? err.message : '数据加载失败');
      setLoading(false);
    }
  }, [options]);

  // 处理文章数据
  const processedArticles = useMemo(() => {
    let filtered = [...articlesData.articles];

    // 分类筛选
    if (options.categoryId && options.categoryId !== 'all') {
      filtered = filtered.filter(article => article.categoryId === options.categoryId);
    }

    // 精选筛选
    if (options.featured !== undefined) {
      filtered = filtered.filter(article => article.featured === options.featured);
    }

    // 搜索筛选
    if (options.searchKeyword) {
      const keyword = options.searchKeyword.toLowerCase();
      filtered = filtered.filter(article => 
        article.title.toLowerCase().includes(keyword) ||
        article.excerpt.toLowerCase().includes(keyword) ||
        article.tags?.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    // 排序（按日期倒序）
    filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // 分页
    if (options.pageSize && options.page) {
      const startIndex = (options.page - 1) * options.pageSize;
      const endIndex = startIndex + options.pageSize;
      filtered = filtered.slice(startIndex, endIndex);
    }

    return filtered;
  }, [options]);

  // 分类数据
  const categories = useMemo(() => {
    return articlesData.categories.map(cat => ({
      id: cat.id,
      label: cat.label,
      data: { color: cat.color }
    }));
  }, []);

  // 筛选文章
  const filterArticles = (newOptions: UseArticlesOptions) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  };

  // 获取单篇文章
  const getArticleById = (id: string | number): Article | undefined => {
    return articlesData.articles.find(article => article.id.toString() === id.toString());
  };

  // 获取相关文章
  const getRelatedArticles = (articleId: string | number, limit = 3): Article[] => {
    const currentArticle = getArticleById(articleId);
    if (!currentArticle) return [];

    // 基于分类和标签推荐相关文章
    const related = articlesData.articles
      .filter(article => 
        article.id !== articleId && (
          article.categoryId === currentArticle.categoryId ||
          article.tags?.some(tag => currentArticle.tags?.includes(tag))
        )
      )
      .slice(0, limit);

    return related;
  };

  return {
    articles: processedArticles,
    categories,
    total: articlesData.articles.length,
    loading,
    error,
    filterArticles,
    getArticleById,
    getRelatedArticles
  };
};
