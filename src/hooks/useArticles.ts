import { useState, useMemo } from 'react';
import { CategoryItem } from '@/components/CategoryTabs';
import { useArticleData } from '@/hooks/useDynamicData';

// 更新Article接口以匹配JSON数据结构
export interface Article {
  /** 文章ID */
  id: string | number;
  /** 文章标题 */
  title: string;
  /** 文章摘要 */
  excerpt?: string;
  /** 发布日期 */
  date: string;
  /** 浏览量 */
  views?: number;
  /** 分类 */
  category?: string;
  /** 分类ID */
  categoryId?: string;
  /** 封面图片 */
  image?: string;
  /** 作者 */
  author?: string;
  /** 文章链接 */
  href?: string;
  /** 标签 */
  tags?: string[];
  /** 是否为精选文章 */
  featured?: boolean;
  /** 自定义数据 */
  data?: any;
}

export interface UseArticlesOptions {
  /** 分类筛选 */
  categoryId?: string;
  /** 是否为精选文章 */
  featured?: boolean;
  /** 每页数量 */
  pageSize?: number;
  /** 当前页码 */
  page?: number;
  /** 搜索关键词 */
  searchKeyword?: string;
  /** 数据文件名 */
  dataFile?: string;
}

export interface UseArticlesReturn {
  /** 文章列表 */
  articles: Article[];
  /** 分类列表 */
  categories: CategoryItem[];
  /** 总数量 */
  total: number;
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 筛选文章 */
  filterArticles: (options: UseArticlesOptions) => void;
  /** 获取单篇文章 */
  getArticleById: (id: string | number) => Article | undefined;
  /** 获取相关文章 */
  getRelatedArticles: (articleId: string | number, limit?: number) => Article[];
}

export const useArticles = (initialOptions: UseArticlesOptions = {}): UseArticlesReturn => {
  const [options, setOptions] = useState<UseArticlesOptions>(initialOptions);

  // 使用动态数据Hook
  const dataFile = options.dataFile || 'news.json';
  const { data: articlesData, loading, error } = useArticleData(dataFile);

  // 处理文章数据
  const processedArticles = useMemo(() => {
    if (!articlesData?.articles) return [];

    let filtered = [...articlesData.articles];

    // 分类筛选
    if (options.categoryId && options.categoryId !== 'all') {
      filtered = filtered.filter(article => article.categoryId === options.categoryId);
    }

    // 精选筛选
    if (options.featured !== undefined) {
      filtered = filtered.filter(article => article.featured === options.featured);
    }

    // 搜索筛选
    if (options.searchKeyword) {
      const keyword = options.searchKeyword.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(keyword) ||
        (article.excerpt && article.excerpt.toLowerCase().includes(keyword)) ||
        article.tags?.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    // 排序（按日期倒序）
    filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // 分页
    if (options.pageSize && options.page) {
      const startIndex = (options.page - 1) * options.pageSize;
      const endIndex = startIndex + options.pageSize;
      filtered = filtered.slice(startIndex, endIndex);
    }

    return filtered;
  }, [articlesData, options]);

  // 分类数据
  const categories = useMemo(() => {
    if (!articlesData?.categories) return [];

    return articlesData.categories.map(cat => ({
      id: cat.id,
      label: cat.label,
      value: cat.value,
      data: {}
    }));
  }, [articlesData]);

  // 筛选文章
  const filterArticles = (newOptions: UseArticlesOptions) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  };

  // 获取单篇文章
  const getArticleById = (id: string | number): Article | undefined => {
    if (!articlesData?.articles) return undefined;
    return articlesData.articles.find(article => article.id.toString() === id.toString());
  };

  // 获取相关文章
  const getRelatedArticles = (articleId: string | number, limit = 3): Article[] => {
    if (!articlesData?.articles) return [];

    const currentArticle = getArticleById(articleId);
    if (!currentArticle) return [];

    // 基于分类和标签推荐相关文章
    const related = articlesData.articles
      .filter(article =>
        article.id !== articleId && (
          article.categoryId === currentArticle.categoryId ||
          article.tags?.some(tag => currentArticle.tags?.includes(tag))
        )
      )
      .slice(0, limit);

    return related;
  };

  return {
    articles: processedArticles,
    categories,
    total: articlesData?.articles?.length || 0,
    loading,
    error,
    filterArticles,
    getArticleById,
    getRelatedArticles
  };
};
