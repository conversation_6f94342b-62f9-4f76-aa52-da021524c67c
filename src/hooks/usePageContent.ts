import { BreadcrumbItem } from '@/components/Breadcrumb';
import { CarouselItem } from '@/components/Carousel';
import pageContentData from '@/data/pages/content.json';

export interface HeroContent {
  title: string;
  description: string;
  backgroundImage: string;
  theme: string; // 改为更灵活的字符串类型
  button?: {
    text: string;
    variant: string; // 改为更灵活的字符串类型
  };
}

export interface StatItem {
  icon: string;
  value: string;
  label: string;
  color: string;
}

export interface PageContent {
  hero: HeroContent;
  breadcrumb: BreadcrumbItem[];
  stats?: StatItem[]; // 可选属性
}

export interface UsePageContentReturn {
  /** 获取页面内容 */
  getPageContent: (pageName: string) => PageContent | null;
  /** 获取轮播图数据 */
  getCarouselItems: () => CarouselItem[];
  /** 获取科室卡片数据 */
  getDepartmentCards: () => Array<{
    id: number;
    image: string;
    title: string;
    subtitle: string;
    href: string;
  }>;
  /** 获取统计数据 */
  getStatsData: () => StatItem[];
}

export const usePageContent = (): UsePageContentReturn => {
  
  // 获取页面内容
  const getPageContent = (pageName: string): PageContent | null => {
    const pageData = pageContentData.pages[pageName as keyof typeof pageContentData.pages];
    if (!pageData) return null;

    return {
      hero: pageData.hero,
      breadcrumb: pageData.breadcrumb,
      stats: 'stats' in pageData ? pageData.stats : undefined // 安全地处理可选属性
    };
  };

  // 获取轮播图数据
  const getCarouselItems = (): CarouselItem[] => {
    return pageContentData.carousel.map(item => ({
      id: item.id,
      image: item.image,
      title: item.title,
      description: item.description,
      buttonText: item.buttonText,
      buttonHref: item.buttonHref
    }));
  };

  // 获取科室卡片数据
  const getDepartmentCards = () => {
    return pageContentData.departments;
  };

  // 获取统计数据
  const getStatsData = (): StatItem[] => {
    const homeContent = pageContentData.pages.home;
    return homeContent.stats || [];
  };

  return {
    getPageContent,
    getCarouselItems,
    getDepartmentCards,
    getStatsData
  };
};
