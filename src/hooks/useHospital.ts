import { useState, useEffect, useMemo } from 'react';
import { Article } from './useArticles';
import hospitalData from '@/data/hospital/basic.json';

// 医院信息接口
export interface HospitalInfo {
  name: string;
  fullName: string;
  description: string;
  introduction: string;
  established: string;
  type: string;
  level: string;
  address: string;
  phone: string;
  website: string;
}

// 医院数据 Hook
export const useHospital = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 医院基本信息
  const hospitalInfo: HospitalInfo = useMemo(() => {
    return hospitalData.hospitalInfo;
  }, []);

  // 医院各部分数据（转换为 Article 格式以兼容 ArticlesGrid）
  const hospitalSections: Article[] = useMemo(() => {
    return hospitalData.hospitalSections.map(section => ({
      ...section,
      id: section.id.toString(), // 确保 id 是字符串类型
    })).sort((a, b) => (a.order || 0) - (b.order || 0));
  }, []);

  // 根据 ID 获取特定部分
  const getHospitalSection = (id: string | number) => {
    return hospitalSections.find(section => section.id === id.toString());
  };

  // 根据 categoryId 获取部分
  const getHospitalSectionsByCategory = (categoryId: string) => {
    return hospitalSections.filter(section => section.categoryId === categoryId);
  };

  // 获取精选部分
  const getFeaturedSections = () => {
    return hospitalSections.filter(section => section.featured);
  };

  useEffect(() => {
    // 模拟数据加载
    const loadData = async () => {
      try {
        setLoading(true);
        // 这里可以添加实际的数据加载逻辑
        await new Promise(resolve => setTimeout(resolve, 100));
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载医院数据失败');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return {
    // 数据
    hospitalInfo,
    hospitalSections,
    
    // 状态
    loading,
    error,
    
    // 方法
    getHospitalSection,
    getHospitalSectionsByCategory,
    getFeaturedSections,
    
    // 元数据
    metadata: hospitalData.metadata
  };
};

export default useHospital;
