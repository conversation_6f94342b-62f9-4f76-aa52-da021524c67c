import { useState, useEffect } from 'react';

export interface DynamicDataOptions {
  filename: string;
  enableCache?: boolean;
  fallbackToStatic?: boolean;
}

export interface DynamicDataResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  source: 'dynamic' | 'static' | 'default';
}

/**
 * 动态数据获取Hook
 * 支持生产环境动态数据和开发环境静态数据的自动切换
 */
export const useDynamicData = <T = any>(options: DynamicDataOptions): DynamicDataResult<T> => {
  const { filename, enableCache = true, fallbackToStatic = true } = options;
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [source, setSource] = useState<'dynamic' | 'static' | 'default'>('dynamic');

  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (!isMounted) return;
      
      setLoading(true);
      setError(null);

      try {
        // 1. 优先尝试加载动态数据（生产环境）
        if (process.env.NODE_ENV === 'production') {
          try {
            const response = await fetch(`/src/data/dynamic/${filename}`, {
              cache: enableCache ? 'default' : 'no-cache'
            });
            
            if (response.ok) {
              const dynamicData = await response.json();
              if (isMounted) {
                setData(dynamicData);
                setSource('dynamic');
                setLoading(false);
                return;
              }
            }
          } catch (dynamicError) {
            console.warn(`动态数据加载失败: ${filename}`, dynamicError);
          }
        }

        // 2. 降级到静态数据
        if (fallbackToStatic) {
          try {
            const staticData = await import(`@/data/static/articles/${filename}`);
            if (isMounted) {
              setData(staticData.default);
              setSource('static');
              setLoading(false);
              return;
            }
          } catch (staticError) {
            console.warn(`静态数据加载失败: ${filename}`, staticError);
          }
        }

        // 3. 使用默认数据
        if (isMounted) {
          const defaultData = getDefaultData(filename);
          setData(defaultData);
          setSource('default');
          setError('数据加载失败，使用默认数据');
        }

      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : '未知错误');
          setData(getDefaultData(filename));
          setSource('default');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [filename, enableCache, fallbackToStatic]);

  return { data, loading, error, source };
};

/**
 * 获取默认数据结构
 */
const getDefaultData = (filename: string): any => {
  const baseStructure = {
    metadata: {
      lastUpdated: new Date().toISOString(),
      source: 'default',
      version: '1.0.0'
    },
    categories: [
      { id: 'all', label: '全部', value: 'all' }
    ],
    articles: []
  };

  // 根据文件名添加特定分类
  if (filename === 'news.json') {
    baseStructure.categories.push(
      { id: 'hospital-news', label: '医院新闻', value: 'hospital-news' },
      { id: 'media-report', label: '媒体报道', value: 'media-report' },
      { id: 'academic', label: '学术交流', value: 'academic' }
    );
  } else if (filename === 'tcm-science.json') {
    baseStructure.categories.push(
      { id: 'basics', label: '中医基础', value: 'basics' },
      { id: 'health', label: '养生保健', value: 'health' },
      { id: 'herbs', label: '药材知识', value: 'herbs' },
      { id: 'treatment', label: '治疗方法', value: 'treatment' },
      { id: 'prevention', label: '疾病防治', value: 'prevention' }
    );
  } else if (filename === 'therapy-methods.json') {
    baseStructure.categories.push(
      { id: 'acupuncture', label: '针灸治疗', value: 'acupuncture' },
      { id: 'massage', label: '按摩治疗', value: 'massage' },
      { id: 'cupping', label: '拔罐治疗', value: 'cupping' },
      { id: 'others', label: '其他', value: 'others' }
    );
  }

  return baseStructure;
};

/**
 * 专用于文章数据的Hook
 */
export const useArticleData = (filename: string) => {
  return useDynamicData<{
    metadata: {
      lastUpdated: string;
      source: string;
      version: string;
    };
    categories: Array<{
      id: string;
      label: string;
      value: string;
    }>;
    articles: Array<{
      id: number | string;
      title: string;
      excerpt: string;
      date: string;
      category: string;
      categoryId: string;
      image: string;
      href: string;
      tags: string[];
      views: number;
      featured?: boolean;
      wechatUrl?: string;
    }>;
  }>({ filename });
};
