
import { Button } from '@/components/ui/button';
import { ArrowRight, Building, Users, Award } from 'lucide-react';
import HeroSection from '@/components/HeroSection';
import Breadcrumb from '@/components/Breadcrumb';
import { usePageContent } from '@/hooks/usePageContent';
import { usePageTitle } from '@/hooks/usePageTitle';

const Home = () => {
  // 设置页面标题（首页不需要额外标题，只显示医院名称）
  usePageTitle();

  // 使用数据管理层
  const { getPageContent, getStatsData } = usePageContent();

  // 获取页面内容
  const pageContent = getPageContent('home');
  const statsData = getStatsData();

  if (!pageContent) {
    return <div>页面内容加载失败</div>;
  }

  // 图标映射
  const iconMap = {
    Building,
    Users,
    Award
  };

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroSection
        title={pageContent.hero.title}
        description={pageContent.hero.description}
        backgroundImage={pageContent.hero.backgroundImage}
        theme={pageContent.hero.theme}
        height="lg"
        button={{
          text: pageContent.hero.button?.text || '了解更多',
          variant: pageContent.hero.button?.variant || 'secondary',
          icon: ArrowRight
        }}
      />

      {/* Main Content */}
      <div className="flex-1 p-4 sm:p-6 md:p-8 lg:p-12">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <Breadcrumb items={pageContent.breadcrumb} />

          {/* Title */}
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-center mb-8 sm:mb-12 lg:mb-16 text-gray-800">大连东海医院</h2>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 mb-8 sm:mb-12 lg:mb-16">
            {/* Left Column - Image */}
            <div className="space-y-4 sm:space-y-6">
              <div className="rounded-lg overflow-hidden shadow-lg">
                <img 
                  src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="集团概览"
                  className="w-full h-48 sm:h-56 md:h-64 lg:h-72 object-cover"
                />
              </div>
              <div className="bg-red-50 p-4 sm:p-6 lg:p-8 rounded-lg">
                <h3 className="text-base sm:text-lg lg:text-xl font-semibold text-red-600 mb-3 sm:mb-4">集团概况</h3>
                <p className="text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                  大连东海医院是一家中医药产业生态公司，涵盖中医医疗、生物科技、康养养老、药膳餐饮和互联网医疗的综合性...
                </p>
                <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50 text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-6">
                  → 进入
                </Button>
              </div>
            </div>

            {/* Right Column - Image */}
            <div className="space-y-4 sm:space-y-6">
              <div className="rounded-lg overflow-hidden shadow-lg">
                <img 
                  src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="企业文化"
                  className="w-full h-48 sm:h-56 md:h-64 lg:h-72 object-cover"
                />
              </div>
              <div className="bg-blue-50 p-4 sm:p-6 lg:p-8 rounded-lg">
                <h3 className="text-base sm:text-lg lg:text-xl font-semibold text-blue-600 mb-3 sm:mb-4">企业文化</h3>
                <p className="text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                  传承中医千年精华，坚持专业化，国际化，数字化方向发展，致力于为人类健康事业做出更大贡献...
                </p>
                <Button variant="outline" className="text-blue-600 border-blue-600 hover:bg-blue-50 text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-6">
                  → 进入
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mt-8 sm:mt-12 lg:mt-16">
            {statsData.map((stat, index) => {
              const IconComponent = iconMap[stat.icon as keyof typeof iconMap];
              const colorClasses = {
                red: 'text-red-600',
                blue: 'text-blue-600',
                green: 'text-green-600',
                purple: 'text-purple-600',
                gray: 'text-gray-600'
              };

              return (
                <div
                  key={index}
                  className={`text-center p-6 sm:p-8 lg:p-10 bg-white rounded-lg shadow-md border hover:shadow-lg transition-shadow ${
                    index === 2 ? 'sm:col-span-2 lg:col-span-1' : ''
                  }`}
                >
                  <IconComponent className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 ${colorClasses[stat.color as keyof typeof colorClasses]} mx-auto mb-4 sm:mb-6`} />
                  <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 sm:mb-3">{stat.value}</h3>
                  <p className="text-sm sm:text-base lg:text-lg text-gray-600">{stat.label}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
