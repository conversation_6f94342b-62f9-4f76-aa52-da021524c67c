import React, { useState } from 'react';
import { RefreshCw, MessageCircle, Calendar, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import HeroSection from '@/components/HeroSection';
import Breadcrumb from '@/components/Breadcrumb';
import CategoryTabs from '@/components/CategoryTabs';
import WechatArticlesList from '@/components/WechatArticlesList';
import Title from '@/components/Title';
import { useWechatArticles, WECHAT_CATEGORIES, formatUpdateTime } from '@/hooks/useWechatArticles';

const WechatArticles = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  // 使用微信文章Hook
  const {
    articles,
    total,
    loading,
    error,
    lastUpdate,
    accountInfo,
    refresh,
    filterArticles,
    getCategoryStats
  } = useWechatArticles({
    category: selectedCategory === 'all' ? undefined : selectedCategory as any,
    autoRefresh: true,
    refreshInterval: 300000 // 5分钟自动刷新
  });

  // 面包屑导航
  const breadcrumbs = [
    { label: '首页', href: '/' },
    { label: '微信公众号', href: '/wechat' },
    { label: '文章列表', href: '/wechat/articles' }
  ];

  // 处理分类切换
  const handleCategoryChange = (categoryId: string | number) => {
    setSelectedCategory(categoryId.toString());
    filterArticles({ 
      category: categoryId === 'all' ? 'all' : categoryId as any 
    });
  };

  // 处理刷新
  const handleRefresh = async () => {
    await refresh();
  };

  // 获取分类统计
  const categoryStats = getCategoryStats();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <HeroSection
        title="微信公众号文章"
        description="大连协和中西医结合医院官方微信公众号最新文章"
        backgroundImage="https://images.unsplash.com/photo-*************-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
        theme="red"
        height="md"
        button={{
          text: "关注公众号",
          href: "#qrcode",
          variant: "default"
        }}
      />

      <div className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* 面包屑导航 */}
          <Breadcrumb items={breadcrumbs} className="mb-6" />

          {/* 公众号信息卡片 */}
          {accountInfo && (
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <MessageCircle className="w-8 h-8 text-red-600" />
                    <div>
                      <CardTitle className="text-xl">{accountInfo.name}</CardTitle>
                      <CardDescription>
                        微信号: {accountInfo.wechatId} | 主体: {accountInfo.entity}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {lastUpdate && (
                      <Badge variant="outline" className="text-xs">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatUpdateTime(lastUpdate)}
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRefresh}
                      disabled={loading}
                      className="text-xs"
                    >
                      <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                      刷新
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  {WECHAT_CATEGORIES.map(category => (
                    <div key={category.id} className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {categoryStats[category.value] || 0}
                      </div>
                      <div className="text-sm text-gray-600">{category.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 分类标签 */}
          <div className="mb-8">
            <CategoryTabs
              categories={WECHAT_CATEGORIES}
              selectedCategory={selectedCategory}
              onCategoryChange={handleCategoryChange}
              theme="red"
              showCount={true}
              counts={categoryStats}
            />
          </div>

          {/* 文章列表标题 */}
          <div className="mb-6">
            <Title
              title={`${WECHAT_CATEGORIES.find(cat => cat.value === selectedCategory)?.label || '全部文章'}`}
              size="md"
              align="left"
              className="mb-2"
            />
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <TrendingUp className="w-4 h-4" />
              <span>共 {total} 篇文章</span>
              {selectedCategory !== 'all' && (
                <span>• 当前分类: {articles.length} 篇</span>
              )}
            </div>
          </div>

          {/* 错误状态 */}
          {error && (
            <Card className="mb-6 border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 text-red-600">
                  <MessageCircle className="w-5 h-5" />
                  <span>加载失败: {error}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    className="ml-auto"
                  >
                    重试
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 加载状态 */}
          {loading && (
            <div className="text-center py-12">
              <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">正在加载微信文章...</p>
            </div>
          )}

          {/* 文章列表 */}
          {!loading && (
            <WechatArticlesList
              articles={articles}
              showCategory={selectedCategory === 'all'}
              showDate={true}
              showSource={false}
              showExternalIcon={true}
              theme="red"
              spacing="normal"
              showHoverEffect={true}
              onArticleClick={(article, index) => {
                console.log('点击文章:', article.title, '索引:', index);
              }}
            />
          )}

          {/* 公众号二维码区域 */}
          <div id="qrcode" className="mt-16">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-xl">关注我们的微信公众号</CardTitle>
                <CardDescription>
                  扫描下方二维码，获取最新医疗资讯和健康科普
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                  <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <MessageCircle className="w-12 h-12 mx-auto mb-2" />
                      <p className="text-sm">微信公众号二维码</p>
                      <p className="text-xs mt-1">{accountInfo?.wechatId}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4 text-sm text-gray-600">
                  <p>微信号: <span className="font-mono">{accountInfo?.wechatId}</span></p>
                  <p>公众号名称: {accountInfo?.name}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WechatArticles;
