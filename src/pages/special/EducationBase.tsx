import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import ArticlesList from '@/components/ArticlesList';
import ArticlesGrid from '@/components/ArticlesGrid';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import educationBaseData from '@/data/education-base.json';

const EducationBase: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 获取基地数据
  const { baseInfo, news } = educationBaseData;

  // 构建基地介绍文章数据
  const introductionArticle = {
    id: 'education-base-intro',
    title: baseInfo.introduction.title,
    excerpt: baseInfo.introduction.content,
    image: baseInfo.introduction.image,
    category: '基地介绍',
    date: '',
    href: '#'
  };

  // 限制显示数量（两行内容，假设每行3个）
  const displayNews = news.slice(0, 6);

  return (
    <PageTemplate
      title={baseInfo.title}
      subtitle={baseInfo.description}
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: baseInfo.title,
        description: baseInfo.description,
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 基地介绍区域 - 使用 ArticlesList 组件 */}
      <section className="mb-12">
        <ArticlesList
          articles={[introductionArticle]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={false}
          imageSize="lg"
          theme="red"
          spacing="normal"
          showHoverEffect={false}
        />
      </section>

      {/* 教育基地新闻区域 */}
      <section className="mb-12">
        {/* 标题 - 使用 Title 组件 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="教育基地新闻"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>

        {/* 新闻网格 */}
        <ArticlesGrid
          articles={displayNews}
          columns={{ sm: 1, md: 2, lg: 3, xl: 3 }}
          showImage={true}
          showCategory={false}
          showDate={true}
          showViews={true}
          showReadMore={false}
          imageAspectRatio="video"
          theme="red"
          spacing="normal"
          showHoverEffect={true}
          showImageZoom={true}
        />

        {/* 查看更多按钮 */}
        {news.length > 6 && (
          <div className="text-center mt-8">
            <button className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
              查看更多
            </button>
          </div>
        )}
      </section>
    </PageTemplate>
  );
};

export default EducationBase;
