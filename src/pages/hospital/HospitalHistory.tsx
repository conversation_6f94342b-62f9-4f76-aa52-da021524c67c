import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import ArticlesList from '@/components/ArticlesList';
import CardComponent from '@/components/CardComponent';
import HorizontalScrollContainer from '@/components/HorizontalScrollContainer';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface HistoryData {
  timeline: Array<{
    year: string;
    title: string;
    description: string;
    image: string;
  }>;
  awards: Array<{
    title: string;
    description: string;
    image: string;
    year?: string;
  }>;
}

// 临时内联数据，避免 JSON 导入问题
const hospitalHistoryData: HistoryData = {
  timeline: [
    {
      year: "2006-2012",
      title: "规模扩张期",
      description: "完成本科升级，国际业务扩大。医院从传统中医诊所发展为综合性医疗机构，引进现代化医疗设备，建立标准化诊疗流程。在此期间，医院床位数从50张增加到200张，医护人员队伍不断壮大，服务质量显著提升。",
      image: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2012-2019",
      title: "跨越腾飞期",
      description: "国内外荣誉颇丰，产业规模进一步扩大。医院获得多项国家级认证，成为地区重点中医医院。建立了完善的人才培养体系，与多所知名医学院校建立合作关系。同时，积极开展国际交流，与海外医疗机构建立友好合作关系。",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2020-现今",
      title: "创新布局期",
      description: "紧手高新产业，开启云端布局。医院全面拥抱数字化转型，建立智慧医疗平台，实现线上线下一体化服务。在疫情期间，医院积极承担社会责任，为抗疫工作做出重要贡献。目前正在筹建新的医疗综合体，将进一步提升服务能力。",
      image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
  ],
  awards: [
    {
      title: "国家中医药管理局重点专科",
      description: "针灸推拿科获得国家中医药管理局重点专科认定，标志着医院在传统中医领域的专业实力得到国家认可。",
      image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2018"
    },
    {
      title: "省级文明单位",
      description: "连续三年获得省级文明单位称号，体现了医院在精神文明建设和社会责任履行方面的突出表现。",
      image: "https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2019"
    },
    {
      title: "医疗质量安全示范医院",
      description: "获得市卫健委颁发的医疗质量安全示范医院称号，在医疗安全管理和质量控制方面树立了行业标杆。",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2020"
    },
    {
      title: "抗疫先进集体",
      description: "在新冠疫情防控工作中表现突出，获得市政府颁发的抗疫先进集体荣誉称号，彰显了医院的社会担当。",
      image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2021"
    },
    {
      title: "智慧医院建设先进单位",
      description: "在数字化转型和智慧医院建设方面成效显著，获得省卫健委颁发的智慧医院建设先进单位称号。",
      image: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2022"
    },
    {
      title: "中医药传承创新示范基地",
      description: "被认定为中医药传承创新示范基地，在中医药文化传承和技术创新方面发挥重要作用。",
      image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      year: "2023"
    }
  ]
};

const HospitalHistory: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);
  const [selectedTimelineIndex, setSelectedTimelineIndex] = useState(0);
  const historyData = hospitalHistoryData as HistoryData;

  // 添加错误处理
  if (!historyData || !historyData.timeline || historyData.timeline.length === 0) {
    return (
      <PageTemplate
        title="医院历程"
        subtitle="二十年发展历程，见证中医药事业的传承与发展"
        breadcrumbs={breadcrumbs}
        showHero={true}
        hero={{
          title: "医院历程",
          description: "二十年砥砺前行，铸就中医药发展新篇章",
          backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
          theme: "red",
          height: "md"
        }}
      >
        <div className="text-center py-12">
          <p className="text-gray-600">数据加载中...</p>
        </div>
      </PageTemplate>
    );
  }

  const handleTimelineClick = (index: number) => {
    setSelectedTimelineIndex(index);
  };

  const handleTimelineNavigation = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && selectedTimelineIndex > 0) {
      setSelectedTimelineIndex(selectedTimelineIndex - 1);
    } else if (direction === 'next' && selectedTimelineIndex < historyData.timeline.length - 1) {
      setSelectedTimelineIndex(selectedTimelineIndex + 1);
    }
  };

  const selectedTimelineItem = historyData.timeline[selectedTimelineIndex];

  return (
    <PageTemplate
      title="医院历程"
      subtitle="二十年发展历程，见证中医药事业的传承与发展"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "医院历程",
        description: "二十年砥砺前行，铸就中医药发展新篇章",
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 发展历程标题 */}
      <div className="mb-12">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="发展历程"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 时间线组件 */}
      <div className="mb-16">
        <div className="relative">
          {/* 移动端导航按钮 */}
          <div className="flex items-center justify-between md:hidden mb-6">
            <button
              onClick={() => handleTimelineNavigation('prev')}
              disabled={selectedTimelineIndex === 0}
              className={cn(
                "p-2 rounded-full border-2 transition-colors",
                selectedTimelineIndex === 0
                  ? "border-gray-300 text-gray-300 cursor-not-allowed"
                  : "border-red-600 text-red-600 hover:bg-red-50"
              )}
            >
              <ChevronLeft className="w-5 h-5" />
            </button>

            <div className="text-center">
              <div className="text-lg font-semibold text-red-600">
                {selectedTimelineItem.year}
              </div>
              <div className="text-sm text-gray-600">
                {selectedTimelineItem.title}
              </div>
            </div>

            <button
              onClick={() => handleTimelineNavigation('next')}
              disabled={selectedTimelineIndex === historyData.timeline.length - 1}
              className={cn(
                "p-2 rounded-full border-2 transition-colors",
                selectedTimelineIndex === historyData.timeline.length - 1
                  ? "border-gray-300 text-gray-300 cursor-not-allowed"
                  : "border-red-600 text-red-600 hover:bg-red-50"
              )}
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>

          {/* 桌面端时间线 */}
          <div className="hidden md:block">
            <div className="relative">
              {/* 贯穿整个时间线的长箭头 */}
              <div className="absolute top-2 left-2 right-2 h-0.5 flex items-center">
                {/* 箭头主体 - 渐变红色线条 */}
                <div className="flex-1 h-0.5 bg-gradient-to-r from-red-400 to-red-600"></div>
                {/* 箭头头部 */}
                <div className="w-0 h-0 border-l-[8px] border-l-red-600 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
              </div>

              {/* 时间节点 */}
              <div className="flex justify-between items-start relative">
                {historyData.timeline.map((item, index) => (
                  <div key={index} className="flex flex-col items-center cursor-pointer group relative" onClick={() => handleTimelineClick(index)}>
                    {/* 时间点圆圈 */}
                    <div
                      className={cn(
                        "w-4 h-4 rounded-full border-4 transition-all duration-300 mb-4 relative z-10",
                        index === selectedTimelineIndex
                          ? "bg-red-600 border-red-600 scale-125"
                          : "bg-white border-gray-400 group-hover:border-red-400"
                      )}
                    ></div>

                    {/* 年份标签 */}
                    <div
                      className={cn(
                        "text-center transition-colors duration-300",
                        index === selectedTimelineIndex
                          ? "text-red-600 font-semibold"
                          : "text-gray-600 group-hover:text-red-500"
                      )}
                    >
                      <div className="text-lg font-medium">{item.year}</div>
                      <div className="text-sm mt-1">{item.title}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 动态内容展示区域 */}
      <div className="mb-16">
        <ArticlesList
          articles={[
            {
              id: selectedTimelineIndex.toString(),
              title: selectedTimelineItem.title,
              excerpt: selectedTimelineItem.description,
              image: selectedTimelineItem.image,
              category: selectedTimelineItem.year,
              date: selectedTimelineItem.year,
              href: '#'
            }
          ]}
          imageSize="lg"
          showCategory={true}
          showDate={false}
          showViews={false}
          showReadMore={false}
          theme="red"
        />
      </div>

      {/* 奖项荣誉标题 */}
      <div className="my-24">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="奖项荣誉"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 奖项荣誉展示 - 响应式横向滚动布局 */}
      <section className="mb-12">
        <HorizontalScrollContainer
          showArrows={true}
          arrowTheme="red"
          scrollStep={200}
          responsive={{
            desktop: true,
            tablet: true,
            mobile: true
          }}
          gridConfig={{
            cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6',
            gap: 'gap-4'
          }}
          scrollConfig={{
            gap: 'gap-4',
            minWidth: 'min-w-[200px] sm:min-w-[240px] md:min-w-[280px]',
            centerAlign: false
          }}
        >
          {historyData.awards.map((award, index) => (
            <CardComponent
              key={index}
              id={index}
              title={award.title}
              subtitle={award.description}
              href="#"
              image={award.image}
              imageAlt={award.title}
              size="md"
              imageAspectRatio="square"
              showHoverEffect={true}
              showImageZoom={true}
              showShadow={true}
              borderRadius="lg"
              textAlign="center"
              textTheme="red"
              backgroundColor="white"
            />
          ))}
        </HorizontalScrollContainer>
      </section>
    </PageTemplate>
  );
};

export default HospitalHistory;
