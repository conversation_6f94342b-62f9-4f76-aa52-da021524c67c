import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import ArticlesGrid from '@/components/ArticlesGrid';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import { useHospital } from '@/hooks/useHospital';

const HospitalIntroduction: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 使用医院数据 Hook
  const { hospitalInfo, hospitalSections, loading, error } = useHospital();

  // 如果正在加载
  if (loading) {
    return (
      <PageTemplate
        title="医院简介"
        subtitle="了解大连东海医院的全貌与发展"
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-500">加载中...</div>
        </div>
      </PageTemplate>
    );
  }

  // 如果有错误
  if (error) {
    return (
      <PageTemplate
        title="医院简介"
        subtitle="了解大连东海医院的全貌与发展"
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-red-500">加载失败: {error}</div>
        </div>
      </PageTemplate>
    );
  }



  return (
    <PageTemplate
      title="医院简介"
      subtitle="了解大连东海医院的全貌与发展"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "医院简介",
        description: "传承中医精髓，服务人民健康 - 大连东海医院",
        backgroundImage: "https://images.unsplash.com/photo-1551190822-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 医院名称标题 */}
      <div className="mb-12">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title={hospitalInfo.name}
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 医院各部分介绍网格 */}
      <section className="mb-12">
        <ArticlesGrid
          articles={hospitalSections}
          columns={{ sm: 1, md: 2, lg: 2, xl: 2 }}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={true}
          readMoreText="了解更多"
          theme="red"
          spacing="normal"
          showHoverEffect={true}
        />
      </section>

    </PageTemplate>
  );
};

export default HospitalIntroduction;
