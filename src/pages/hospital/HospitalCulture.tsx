import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import ArticlesList from '@/components/ArticlesList';
import CardComponent from '@/components/CardComponent';
import CoreValuesSection from '@/components/CoreValuesSection';
import HorizontalScrollContainer from '@/components/HorizontalScrollContainer';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import { useHospitalCulture } from '@/hooks/useHospitalCulture';

const HospitalCulture: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 使用医院文化数据 Hook
  const {
    pageInfo,
    heroSection,
    cultureIntroduction,
    cultureIntroductionArticle,
    coreValues,
    culturalGalleryTitle,
    culturalGallery,
    loading,
    error
  } = useHospitalCulture();

  // 如果正在加载
  if (loading) {
    return (
      <PageTemplate
        title={pageInfo.title}
        subtitle={pageInfo.subtitle}
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-500">加载中...</div>
        </div>
      </PageTemplate>
    );
  }

  // 如果有错误
  if (error) {
    return (
      <PageTemplate
        title={pageInfo.title}
        subtitle={pageInfo.subtitle}
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-red-500">加载失败: {error}</div>
        </div>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate
      title={pageInfo.title}
      subtitle={pageInfo.subtitle}
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: heroSection.title,
        description: heroSection.description,
        backgroundImage: heroSection.backgroundImage,
        theme: heroSection.theme as "red" | "blue" | "green" | "purple" | "gray",
        height: heroSection.height as "md" | "lg" | "sm" | "xl" | "auto"
      }}
    >
      {/* 医院文化介绍 - ArticlesList */}
      <section className="mb-12">
        <ArticlesList
          articles={[cultureIntroductionArticle]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={true}
          readMoreText={cultureIntroduction.readMoreText}
          theme="red"
          imageSize="xl"
          spacing="normal"
          showHoverEffect={true}
        />
      </section>

      {/* 核心价值观 */}
      <CoreValuesSection
        title={coreValues.title}
        subtitle={coreValues.subtitle}
        layout={coreValues.layout as 'default' | 'enterprise'}
        values={coreValues.values}
        theme="red"
        className="mb-12"
      />

      {/* 文化掠影标题 */}
      <div className="mb-12">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title={culturalGalleryTitle.title}
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 文化掠影 - 响应式横向滚动布局 */}
      <section className="mb-12">
        <HorizontalScrollContainer
          showArrows={true}
          arrowTheme="red"
          scrollStep={200}
          responsive={{
            desktop: true,
            tablet: true,
            mobile: true
          }}
          gridConfig={{
            cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6',
            gap: 'gap-4'
          }}
          scrollConfig={{
            gap: 'gap-4',
            minWidth: 'min-w-[200px] sm:min-w-[240px] md:min-w-[280px]',
            centerAlign: false
          }}
        >
          {culturalGallery.map((item) => (
            <CardComponent
              key={item.id}
              id={item.id}
              title={item.title}
              subtitle={item.subtitle}
              href={item.href}
              image={item.image}
              imageAlt={item.imageAlt}
              size="md"
              imageAspectRatio="square"
              showHoverEffect={true}
              showImageZoom={true}
              showShadow={true}
              borderRadius="lg"
              textAlign="center"
              textTheme="red"
              backgroundColor="white"
            />
          ))}
        </HorizontalScrollContainer>
      </section>
    </PageTemplate>
  );
};

export default HospitalCulture;
