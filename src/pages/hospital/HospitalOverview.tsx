import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import VideoPlayer from '@/components/VideoPlayer';
import ArticlesList from '@/components/ArticlesList';
import Carousel from '@/components/Carousel';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import { useHospitalOverview } from '@/hooks/useHospitalOverview';

const HospitalOverview: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 使用医院概况数据 Hook
  const {
    pageInfo,
    heroSection,
    hospitalName,
    videoSection,
    hospitalIntroduction,
    hospitalIntroductionArticle,
    otherContentTitle,
    carouselSection,
    loading,
    error
  } = useHospitalOverview();

  // 如果正在加载
  if (loading) {
    return (
      <PageTemplate
        title={pageInfo.title}
        subtitle={pageInfo.subtitle}
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-500">加载中...</div>
        </div>
      </PageTemplate>
    );
  }

  // 如果有错误
  if (error) {
    return (
      <PageTemplate
        title={pageInfo.title}
        subtitle={pageInfo.subtitle}
        breadcrumbs={breadcrumbs}
      >
        <div className="flex justify-center items-center py-12">
          <div className="text-red-500">加载失败: {error}</div>
        </div>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate
      title={pageInfo.title}
      subtitle={pageInfo.subtitle}
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: heroSection.title,
        description: heroSection.description,
        backgroundImage: heroSection.backgroundImage,
        theme: heroSection.theme as "red" | "blue" | "green" | "purple" | "gray",
        height: heroSection.height as "md" | "lg" | "sm" | "xl" | "auto"
      }}
    >
      {/* 医院名称标题 */}
      <div className="mb-12">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title={hospitalName.name}
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 视频播放器 */}
      <section className="mb-12">
        <VideoPlayer
          sources={videoSection.videoSources}
          poster={videoSection.poster}
          title={videoSection.title}
          aspectRatio={videoSection.aspectRatio as "video" | "square" | "wide" | "cinema"}
          size={videoSection.size as "sm" | "md" | "lg" | "xl" | "full"}
          responsive={true}
          controls={true}
          customControls={true}
          className="w-full"
        />
      </section>

      {/* 医院介绍 - ArticlesList */}
      <section className="mb-12">
        <ArticlesList
          articles={[hospitalIntroductionArticle]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={true}
          readMoreText={hospitalIntroduction.readMoreText}
          theme="red"
          imageSize="lg"
          spacing="normal"
          showHoverEffect={true}
        />
      </section>

      {/* 其他内容标题 */}
      <div className="mb-12">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title={otherContentTitle.title}
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 轮播图组件 */}
      <section className="mb-12">
        <Carousel
          items={carouselSection.items}
          autoPlay={carouselSection.autoPlay}
          autoPlayInterval={carouselSection.autoPlayInterval}
          infinite={true}
          showIndicators={true}
          showArrows={true}
          showPlayPause={true}
          layout="horizontal"
          imagePosition="left"
          imageWidthRatio={0.4}
          height="md"
        />
      </section>


    </PageTemplate>
  );
};

export default HospitalOverview;
