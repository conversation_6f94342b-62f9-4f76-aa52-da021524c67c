import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import ArticlesList from '@/components/ArticlesList';
import HorizontalScrollContainer from '@/components/HorizontalScrollContainer';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';

const SocialCooperation: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 企业合作数据
  const enterpriseCooperationData = {
    id: '1',
    title: '企业合作',
    excerpt: '大连东海医院积极与本地知名企业建立战略合作关系，为企业员工提供专业的中医药健康服务。我们与大连万达集团、大连港集团、大连重工等知名企业建立了长期合作关系，为企业员工提供健康体检、中医调理、职业病防治等全方位医疗服务。\n\n通过企业合作，我们不仅为企业员工的健康保驾护航，同时也推动了中医药文化在企业中的传播和应用。我们的专业医疗团队定期为合作企业提供健康讲座、中医养生指导等服务，帮助企业建立健康的工作环境。',
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2126&q=80',
    category: '企业合作',
    date: '2024-01-01',
    href: '#'
  };

  // 院校合作数据
  const academicCooperationData = {
    id: '2',
    title: '院校合作',
    excerpt: '大连东海医院与多所知名医学院校建立了深度合作关系，共同推进中医药教育事业的发展。我们与大连医科大学、辽宁中医药大学、大连职业技术学院等院校开展教学实习、科研合作、人才培养等多方面合作。\n\n医院为院校学生提供优质的实习实训平台，让学生在真实的医疗环境中学习和成长。同时，我们与院校共同开展中医药科研项目，推动中医药理论与实践的创新发展。通过院校合作，我们培养了大批优秀的中医药专业人才。',
    image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    category: '院校合作',
    date: '2024-01-01',
    href: '#'
  };

  // 海外业务合作数据
  const internationalCooperationData = {
    id: '3',
    title: '海外业务合作',
    excerpt: '大连东海医院积极响应"一带一路"倡议，与多个国家和地区的医疗机构建立合作关系，推动中医药文化的国际传播。我们与日本、韩国、俄罗斯等国的医疗机构开展学术交流、技术合作、人员培训等项目。\n\n通过海外合作，我们不仅将中医药的独特疗效带给更多国际患者，同时也学习借鉴国外先进的医疗管理经验。我们定期派遣医疗专家赴海外进行学术交流，同时接待国外医疗代表团来院参观学习，促进中外医学文化的交流融合。',
    image: 'https://images.unsplash.com/photo-1569025743873-ea3a9ade89f9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    category: '海外合作',
    date: '2024-01-01',
    href: '#'
  };

  // 合作企业logo数据
  const partnerLogos = [
    {
      id: 1,
      name: '大连万达集团',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    },
    {
      id: 2,
      name: '大连港集团',
      logo: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    },
    {
      id: 3,
      name: '大连重工',
      logo: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    },
    {
      id: 4,
      name: '东软集团',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    },
    {
      id: 5,
      name: '大连化工',
      logo: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    },
    {
      id: 6,
      name: '大连银行',
      logo: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      website: '#'
    }
  ];

  return (
    <PageTemplate
      title="社会合作"
      subtitle="携手社会各界，共同推进中医药事业发展"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "社会合作",
        description: "携手社会各界，共同推进中医药事业发展",
        backgroundImage: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 企业合作标题 */}
      <div className="my-20">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="企业合作"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 企业合作内容 */}
      <section className="mb-12">
        <ArticlesList
          articles={[enterpriseCooperationData]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={false}
          imageSize="lg"
          theme="red"
          spacing="normal"
          showHoverEffect={false}
        />
      </section>

      {/* 合作企业图标展示 */}
      <section className="mb-16">
        <HorizontalScrollContainer
          showArrows={true}
          arrowTheme="red"
          scrollStep={200}
          responsive={{
            desktop: true,
            tablet: true,
            mobile: true
          }}
          gridConfig={{
            cols: 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6',
            gap: 'gap-6'
          }}
          scrollConfig={{
            gap: 'gap-6',
            minWidth: 'min-w-[160px] sm:min-w-[180px] md:min-w-[200px]',
            centerAlign: true
          }}
        >
          {partnerLogos.map((partner) => (
            <div
              key={partner.id}
              className="flex flex-col items-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 group cursor-pointer"
            >
              <div className="w-20 h-20 mb-3 overflow-hidden rounded-lg">
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="w-full h-full object-cover filter grayscale group-hover:grayscale-0 transition-all duration-300"
                />
              </div>
              <span className="text-sm text-gray-600 text-center font-medium group-hover:text-red-600 transition-colors duration-300">
                {partner.name}
              </span>
            </div>
          ))}
        </HorizontalScrollContainer>
      </section>

      {/* 院校合作标题 */}
      <div className="my-20">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="院校合作"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 院校合作内容 */}
      <section className="mb-12">
        <ArticlesList
          articles={[{
            ...academicCooperationData,
            // 通过CSS实现左文本右图布局
          }]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={false}
          imageSize="lg"
          theme="red"
          spacing="normal"
          showHoverEffect={false}
          className="[&_article]:flex-row-reverse"
        />
      </section>

      {/* 海外业务合作标题 */}
      <div className="my-20">
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="海外业务合作"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>
      </div>

      {/* 海外业务合作内容 */}
      <section className="mb-12">
        <ArticlesList
          articles={[internationalCooperationData]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={false}
          imageSize="lg"
          theme="red"
          spacing="normal"
          showHoverEffect={false}
        />
      </section>
    </PageTemplate>
  );
};

export default SocialCooperation;
