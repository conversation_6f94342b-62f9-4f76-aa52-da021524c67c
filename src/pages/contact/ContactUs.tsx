
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { MapPin, Phone, Mail, Clock, Bus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import BaiduMap from '@/components/BaiduMap';

const ContactUs = () => {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative h-48 sm:h-56 md:h-64 bg-gradient-to-r from-purple-600 to-purple-700 flex items-center justify-center text-white">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
          }}
        />
        <div className="relative z-10 text-center px-4">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4">联系我们</h1>
          <p className="text-sm sm:text-base md:text-lg opacity-90">欢迎与东海健康产业集团取得联系</p>
        </div>
      </section>

      {/* Main Content */}
      <div className="flex-1 p-4 sm:p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <nav className="mb-6 sm:mb-8 text-xs sm:text-sm text-gray-500">
            <span>当前位置：</span>
            <Link to="/" className="text-gray-700 hover:text-red-600">首页</Link>
            <span className="mx-1 sm:mx-2">{'>'}</span>
            <Link to="/contact" className="text-gray-700 hover:text-red-600">联系我们</Link>
            <span className="mx-1 sm:mx-2">{'>'}</span>
            <span className="text-red-600">联系方式</span>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12">
            {/* Contact Information */}
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-6 sm:mb-8">大连东海健康产业集团</h2>
              
              <div className="space-y-4 sm:space-y-6">
                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">地址</h3>
                    <p className="text-gray-600 text-sm sm:text-base">辽宁省大连市旅顺口区龙海路168-9</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Phone className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">电话</h3>
                    <p className="text-gray-600 text-sm sm:text-base">0411-39656855</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">传真</h3>
                    <p className="text-gray-600 text-sm sm:text-base">0411-39656818</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">邮编</h3>
                    <p className="text-gray-600 text-sm sm:text-base">116011</p>
                  </div>
                </div>
              </div>

              {/* QR Code */}
              <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-800 mb-3 sm:mb-4 text-sm sm:text-base">添加企业微信</h3>
                <div className="w-24 h-24 sm:w-32 sm:h-32 bg-white border rounded-lg flex items-center justify-center">
                  <span className="text-gray-400 text-xs sm:text-sm">二维码</span>
                </div>
              </div>
            </div>

            {/* Map */}
            <div>
              <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-4 sm:mb-6">位置地图</h3>
              <BaiduMap
                address="辽宁省大连市中山区海军广场街道乐华街32号"
                hospitalName="大连东海医院"
                className="shadow-lg border border-gray-200"
                height="h-64 sm:h-80 md:h-96"
              />
            </div>
          </div>

          {/* Transportation Info */}
          <div className="mt-8 sm:mt-12">
            <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-4 sm:mb-6 flex items-center">
              <Bus className="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-blue-600" />
              公交线路
            </h3>
            <div className="bg-white border rounded-lg p-4 sm:p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 sm:mb-3 text-sm sm:text-base">希望广场站下车</h4>
                  <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                    15路、303路、409路、531路、532路、534路、701路、701路加车、702路、702路加车、710路；
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 sm:mb-3 text-sm sm:text-base">青龙港桥站下车</h4>
                  <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">
                    16路、19路、22路、23路、405路、531路、708路、708路加车、708路区间车、901路；
                  </p>
                </div>
              </div>
              <div className="mt-3 sm:mt-4">
                <h4 className="font-semibold text-gray-800 mb-2 sm:mb-3 text-sm sm:text-base">地铁三号线</h4>
                <p className="text-gray-600 text-xs sm:text-sm">青龙港桥站C口下车</p>
              </div>
            </div>
          </div>

          {/* Hospital Building */}
          <div className="mt-8 sm:mt-12">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1551190822-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                alt="东海健康产业集团大楼"
                className="w-full h-48 sm:h-56 md:h-64 object-cover"
              />
              <div className="p-4 sm:p-6">
                <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2">东海健康产业集团总部</h3>
                <p className="text-sm sm:text-base text-gray-600">现代化的医疗健康产业园区，为您提供全方位的健康服务</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
