
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Mail, Send, Upload, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

const EmailUs = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    contact: '',
    email: '',
    address: '',
    category: '',
    subject: '',
    content: '',
    attachment: null as File | null
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.subject || !formData.content) {
      toast({
        title: "提交失败",
        description: "请填写所有必填字段",
        variant: "destructive"
      });
      return;
    }

    // Simulate email sending
    toast({
      title: "提交成功",
      description: "您的留言已提交，我们会尽快回复您！",
    });

    // Reset form
    setFormData({
      name: '',
      contact: '',
      email: '',
      address: '',
      category: '',
      subject: '',
      content: '',
      attachment: null
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, attachment: file }));
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative h-48 sm:h-56 md:h-64 bg-gradient-to-r from-indigo-600 to-indigo-700 flex items-center justify-center text-white">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1586297135537-94bc9ba060aa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
          }}
        />
        <div className="relative z-10 text-center px-4">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4">在线留言</h1>
          <p className="text-sm sm:text-base md:text-lg opacity-90">Online Message</p>
        </div>
      </section>

      {/* Main Content */}
      <div className="flex-1 p-4 sm:p-6 md:p-8">
        <div className="max-w-4xl mx-auto">
          {/* Breadcrumb */}
          <nav className="mb-6 sm:mb-8 text-xs sm:text-sm text-gray-500">
            <span>当前位置：</span>
            <Link to="/" className="text-gray-700 hover:text-red-600">首页</Link>
            <span className="mx-1 sm:mx-2">{'>'}</span>
            <Link to="/contact" className="text-gray-700 hover:text-red-600">联系我们</Link>
            <span className="mx-1 sm:mx-2">{'>'}</span>
            <span className="text-red-600">在线留言</span>
          </nav>

          {/* Header */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="flex items-center justify-center mb-3 sm:mb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-indigo-600" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">在线留言</h2>
            <p className="text-sm sm:text-base text-gray-600">Online Message</p>
            <Button className="mt-3 sm:mt-4 bg-red-600 hover:bg-red-700 text-sm sm:text-base">
              查看我的回复
            </Button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg border p-4 sm:p-6 md:p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  您的姓名 <span className="text-red-500">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="请输入您的姓名"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>

              {/* Contact */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系方式 <span className="text-red-500">*</span>
                </label>
                <Input
                  type="text"
                  placeholder="请输入联系方式"
                  value={formData.contact}
                  onChange={(e) => handleInputChange('contact', e.target.value)}
                  required
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  电子邮箱 <span className="text-red-500">*</span>
                </label>
                <Input
                  type="email"
                  placeholder="请输入电子邮箱"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  留言类别 <span className="text-red-500">*</span>
                </label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择留言类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cooperation">合作咨询</SelectItem>
                    <SelectItem value="medical">医疗服务</SelectItem>
                    <SelectItem value="complaint">投诉建议</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Address */}
            <div className="mb-4 sm:mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                详细地址 <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                placeholder="请输入详细地址"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            </div>

            {/* Subject */}
            <div className="mb-4 sm:mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                留言主题 <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                placeholder="请输入留言主题"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                required
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {formData.subject.length}/50
              </div>
            </div>

            {/* Content */}
            <div className="mb-4 sm:mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                留言内容 <span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="请输入留言内容"
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                className="min-h-24 sm:min-h-32"
                required
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {formData.content.length}/1500
              </div>
            </div>

            {/* File Upload */}
            <div className="mb-4 sm:mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                上传附件
              </label>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                <Input
                  type="file"
                  onChange={handleFileUpload}
                  className="flex-1"
                  placeholder="请选择上传附件"
                />
                <Button type="button" variant="outline" className="w-full sm:w-auto">
                  <Upload className="w-4 h-4 mr-2" />
                  验证上传
                </Button>
              </div>
            </div>

            {/* Captcha */}
            <div className="mb-6 sm:mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                验证码 <span className="text-red-500">*</span>
              </label>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center">
                <Input
                  type="text"
                  placeholder="请输入验证码"
                  className="flex-1"
                />
                <div className="w-20 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded flex items-center justify-center text-white font-bold text-sm">
                  A8C9
                </div>
                <Button type="button" variant="outline" size="sm" className="w-full sm:w-auto">
                  <RefreshCw className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  换一个
                </Button>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
              <Button type="submit" size="lg" className="bg-red-600 hover:bg-red-700 px-6 sm:px-8">
                <Send className="w-4 h-4 mr-2" />
                提交
              </Button>
              <Button type="button" variant="outline" size="lg" className="px-6 sm:px-8">
                重置
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EmailUs;
