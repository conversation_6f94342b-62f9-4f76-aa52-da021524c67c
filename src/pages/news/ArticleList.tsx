
import { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import HeroSection from '@/components/HeroSection';
import Breadcrumb from '@/components/Breadcrumb';
import CategoryTabs from '@/components/CategoryTabs';
import ArticlesList from '@/components/ArticlesList';
import { useArticles } from '@/hooks/useArticles';
import { usePageContent } from '@/hooks/usePageContent';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';

const ArticleList = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | number>('all');
  const location = useLocation();

  // 使用数据管理层
  const { articles, categories, loading } = useArticles({
    categoryId: selectedCategory === 'all' ? undefined : selectedCategory.toString()
  });
  const { getPageContent } = usePageContent();

  // 获取页面内容和面包屑导航
  const pageContent = getPageContent('articleList');
  const breadcrumbs = getBreadcrumbs(location.pathname);

  if (!pageContent) {
    return <div>页面内容加载失败</div>;
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroSection
        title={pageContent.hero.title}
        description={pageContent.hero.description}
        backgroundImage={pageContent.hero.backgroundImage}
        theme={pageContent.hero.theme}
        height="md"
        button={pageContent.hero.button}
      />

      {/* Main Content */}
      <div className="flex-1 p-4 sm:p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <Breadcrumb items={breadcrumbs} />

          {/* Category Tabs */}
          <CategoryTabs
            categories={categories}
            theme="red"
            onCategoryChange={(category) => setSelectedCategory(category.id)}
          />

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
            </div>
          )}

          {/* Articles List */}
          {!loading && (
            <ArticlesList
              articles={articles}
              theme="red"
              showReadMore={true}
              readMoreText="阅读更多"
              readMoreIcon={ArrowRight}
            />
          )}

          {/* Pagination */}
          <div className="flex justify-center mt-8 sm:mt-12">
            <div className="flex gap-1 sm:gap-2">
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">上一页</Button>
              <Button size="sm" className="bg-red-600 hover:bg-red-700 text-xs sm:text-sm">1</Button>
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">2</Button>
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">3</Button>
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">下一页</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleList;
