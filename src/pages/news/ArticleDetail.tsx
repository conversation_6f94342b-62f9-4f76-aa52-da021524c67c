
import React from 'react';
import { Link, useParams } from 'react-router-dom';
import { Calendar, Eye, Share2, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

const ArticleDetail = () => {
  const { id } = useParams();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-4xl mx-auto">
          <Link to="/articles" className="inline-flex items-center text-red-600 hover:text-red-700 mb-4">
            <ArrowLeft className="w-4 h-4 mr-1" />
            返回新闻列表
          </Link>
          
          <nav className="text-sm text-gray-500 mb-4">
            <span>当前位置：</span>
            <Link to="/" className="text-gray-700 hover:text-red-600">首页</Link>
            <span className="mx-2">{'>'}</span>
            <Link to="/articles" className="text-gray-700 hover:text-red-600">新闻中心</Link>
            <span className="mx-2">{'>'}</span>
            <span className="text-red-600">集团动态</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-4xl mx-auto">
          <article className="bg-white rounded-lg shadow-sm border p-8">
            {/* Article Header */}
            <header className="mb-8 text-center">
              <span className="px-3 py-1 bg-red-100 text-red-600 text-sm font-medium rounded-full">
                集团动态
              </span>
              
              <h1 className="text-3xl font-bold text-gray-800 mt-6 mb-4 leading-tight">
                东海配理工"体医融合"持续推进
              </h1>
              
              <p className="text-xl text-gray-600 mb-6">
                通过科学的运动处方来预防和治疗疾病
              </p>
              
              <div className="flex items-center justify-center gap-6 text-gray-500 text-sm">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Mar 11, 2024</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>阅读次数：2</span>
                </div>
                <Button variant="ghost" size="sm" className="text-gray-500 hover:text-red-600">
                  <Share2 className="w-4 h-4 mr-1" />
                  分享
                </Button>
              </div>
            </header>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed mb-6">
                2024年开年，大连理工大学体育与健康学院与大连东海医院的合作再度迈入新阶段，共同推进「体医融合」工作。
              </p>

              <p className="text-gray-700 leading-relaxed mb-8">
                2024年开年，大连理工大学体育与健康学院与大连东海医院的合作再度迈入新阶段，共同推进「体医融合」工作。这一合作旨在促进医学与运动的结合，强调通过运动来预防病，实现疾病的防范与治疗。而「运动处方」则成为连接体育和医疗的重要桥梁，将医疗机构、健身机构、医生和健身指导师有机地联系在一起。
              </p>

              {/* Featured Image */}
              <div className="my-12">
                <img 
                  src="https://images.unsplash.com/photo-1551190822-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                  alt="体医融合活动"
                  className="w-full rounded-lg shadow-lg"
                />
                <p className="text-sm text-gray-500 text-center mt-2">东海医院体医融合项目现场</p>
              </div>

              <p className="text-gray-700 leading-relaxed mb-6">
                「体医融合」的核心理念是将运动融入到医疗过程中，通过科学的运动处方来预防和治疗疾病。每一张运动处方的制定都应基于严谨的科学研究，确保对患者的干预行为具有科学性和有效性。为了达到这一目标，大连理工大学体育与健康学院与大连东海医院开展了FMS(Functional Movement Screen)的学习和培训。
              </p>

              <div className="bg-gray-50 p-6 rounded-lg my-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">项目亮点</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• 科学的运动处方制定</li>
                  <li>• 专业的FMS功能性动作筛查</li>
                  <li>• 医疗与运动的深度融合</li>
                  <li>• 预防性健康管理理念</li>
                </ul>
              </div>

              <p className="text-gray-700 leading-relaxed">
                通过这一合作项目，我们不仅能够为患者提供更加全面的健康管理服务，还能推动体医融合理念在更广泛领域的应用和发展。
              </p>
            </div>

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  <span>文章来源：东海健康产业集团</span>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Share2 className="w-4 h-4 mr-1" />
                    分享
                  </Button>
                </div>
              </div>
            </footer>
          </article>

          {/* Related Articles */}
          <section className="mt-12">
            <h3 className="text-xl font-bold text-gray-800 mb-6">相关文章</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <Link to="/article/2" className="group">
                <div className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow">
                  <h4 className="font-semibold text-gray-800 group-hover:text-red-600 transition-colors mb-2">
                    东海医院携东海生物助力大连女足2025赛季创佳绩
                  </h4>
                  <p className="text-sm text-gray-500">Dec 27, 2024</p>
                </div>
              </Link>
              <Link to="/article/3" className="group">
                <div className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow">
                  <h4 className="font-semibold text-gray-800 group-hover:text-red-600 transition-colors mb-2">
                    东海医院荣获大连市体医融合发展合作示范单位
                  </h4>
                  <p className="text-sm text-gray-500">Dec 23, 2024</p>
                </div>
              </Link>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default ArticleDetail;
