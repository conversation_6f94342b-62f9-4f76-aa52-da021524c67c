import React, { useState } from 'react';
import PageTemplate from '@/components/PageTemplate';
import HeroSection from '@/components/HeroSection';
import Breadcrumb from '@/components/Breadcrumb';
import CategoryTabs from '@/components/CategoryTabs';
import ArticlesGrid from '@/components/ArticlesGrid';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

const HospitalStyle: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);
  const [selectedCategory, setSelectedCategory] = useState('hospital-area');

  // 分类数据
  const categories = [
    { id: 'hospital-area', label: '院区风采', value: 'hospital-area' },
    { id: 'health-center', label: '体检中心', value: 'health-center' },
    { id: 'health-management', label: '健康管理中心', value: 'health-management' }
  ];

  // 院区风采图片数据
  const hospitalAreaImages = [
    {
      id: '1',
      title: '医院主楼外观',
      excerpt: '现代化的医院建筑，融合传统中医文化元素，展现医院的专业形象和文化底蕴。',
      image: 'https://images.unsplash.com/photo-**********-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '2',
      title: '门诊大厅',
      excerpt: '宽敞明亮的门诊大厅，温馨舒适的就医环境，为患者提供便民服务。',
      image: 'https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '3',
      title: '中医诊疗室',
      excerpt: '传统中医诊疗环境，配备现代化设备，传承与创新相结合。',
      image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '4',
      title: '药房配药区',
      excerpt: '现代化的中药房，严格的质量管控，确保药品安全有效。',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '5',
      title: '康复理疗区',
      excerpt: '专业的康复理疗设备，为患者提供全方位的康复治疗服务。',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '6',
      title: '医护工作站',
      excerpt: '高效的医护工作环境，先进的信息化管理系统，提升医疗服务质量。',
      image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '7',
      title: '病房环境',
      excerpt: '温馨舒适的住院环境，人性化的设计理念，让患者感受家的温暖。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '8',
      title: '医院花园',
      excerpt: '绿意盎然的医院花园，为患者和家属提供休憩放松的空间。',
      image: 'https://images.unsplash.com/photo-1584820927498-cfe5211fd8bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '院区风采',
      date: '2024-01-01',
      href: '#'
    }
  ];

  // 体检中心图片数据
  const healthCenterImages = [
    {
      id: '9',
      title: '体检接待大厅',
      excerpt: '现代化的体检中心接待大厅，提供一站式健康体检服务。',
      image: 'https://images.unsplash.com/photo-**********-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '10',
      title: 'CT检查室',
      excerpt: '先进的CT设备，精准的影像诊断，为健康体检提供可靠保障。',
      image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '11',
      title: '超声检查室',
      excerpt: '专业的超声检查设备，无创检查技术，确保体检过程舒适安全。',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '12',
      title: '心电图检查室',
      excerpt: '标准化的心电图检查环境，专业的医技人员，保证检查结果准确性。',
      image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '13',
      title: '实验室',
      excerpt: '现代化的医学检验实验室，先进的检测设备，确保检验结果精准可靠。',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '14',
      title: '体检休息区',
      excerpt: '舒适的体检休息区域，为体检者提供温馨的等候环境。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '体检中心',
      date: '2024-01-01',
      href: '#'
    }
  ];

  // 健康管理中心图片数据
  const healthManagementImages = [
    {
      id: '15',
      title: '健康咨询室',
      excerpt: '专业的健康管理咨询服务，为客户制定个性化健康管理方案。',
      image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '健康管理中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '16',
      title: '营养指导室',
      excerpt: '专业营养师提供科学的饮食指导，助力健康生活方式的建立。',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '健康管理中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '17',
      title: '健康监测区',
      excerpt: '先进的健康监测设备，实时跟踪健康指标，科学管理健康状况。',
      image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '健康管理中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '18',
      title: '中医养生馆',
      excerpt: '传统中医养生理念与现代健康管理相结合，提供全方位养生服务。',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '健康管理中心',
      date: '2024-01-01',
      href: '#'
    },
    {
      id: '19',
      title: '健康教育室',
      excerpt: '定期开展健康教育讲座，普及健康知识，提升健康素养。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      category: '健康管理中心',
      date: '2024-01-01',
      href: '#'
    }
  ];

  // 根据选中的分类获取对应的图片数据
  const getCurrentImages = () => {
    switch (selectedCategory) {
      case 'hospital-area':
        return hospitalAreaImages;
      case 'health-center':
        return healthCenterImages;
      case 'health-management':
        return healthManagementImages;
      default:
        return hospitalAreaImages;
    }
  };

  return (
    <PageTemplate>
      {/* Hero Section */}
      <HeroSection
        title="医院风采"
        description="展示医院现代化设施与温馨就医环境"
        backgroundImage="https://images.unsplash.com/photo-**********-a9333d879b1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
        theme="red"
        height="md"
        button={{
          text: '了解更多',
          variant: 'secondary',
          icon: ArrowRight
        }}
      />

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <Breadcrumb items={breadcrumbs} />

          {/* Category Tabs */}
          <CategoryTabs
            categories={categories}
            theme="red"
            activeCategory={selectedCategory}
            defaultCategory="hospital-area"
            showAllOption={false}
            onCategoryChange={(category) => setSelectedCategory(category.id)}
          />

          {/* Images Grid */}
          <ArticlesGrid
            articles={getCurrentImages()}
            theme="red"
            columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
            showCategory={false}
            showDate={false}
            showViews={false}
            showReadMore={false}
            showHoverEffect={true}
          />
        </div>
      </div>
    </PageTemplate>
  );
};

export default HospitalStyle;
