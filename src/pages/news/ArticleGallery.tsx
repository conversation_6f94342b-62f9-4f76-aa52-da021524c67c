import { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import HeroSection from '@/components/HeroSection';
import Breadcrumb from '@/components/Breadcrumb';
import CategoryTabs from '@/components/CategoryTabs';
import ArticlesGrid from '@/components/ArticlesGrid';
import { useArticles } from '@/hooks/useArticles';
import { usePageContent } from '@/hooks/usePageContent';

const ArticleGallery = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | number>('all');

  // 使用数据管理层
  const { articles, categories, loading } = useArticles({
    categoryId: selectedCategory === 'all' ? undefined : selectedCategory.toString()
  });
  const { getPageContent } = usePageContent();

  // 获取页面内容
  const pageContent = getPageContent('articleGallery');

  if (!pageContent) {
    return <div>页面内容加载失败</div>;
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroSection
        title={pageContent.hero.title}
        description={pageContent.hero.description}
        backgroundImage={pageContent.hero.backgroundImage}
        theme={pageContent.hero.theme}
        height="md"
        button={pageContent.hero.button}
      />

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <Breadcrumb items={pageContent.breadcrumb} />

          {/* Category Tabs */}
          <CategoryTabs
            categories={categories}
            theme="green"
            onCategoryChange={(category) => setSelectedCategory(category.id)}
          />

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            </div>
          )}

          {/* Articles Grid */}
          {!loading && (
            <ArticlesGrid
              articles={articles}
              theme="green"
              columns={{ sm: 1, md: 2, lg: 3 }}
              showReadMore={true}
              readMoreIcon={ArrowRight}
            />
          )}

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex gap-2">
              <Button variant="outline" size="sm">上一页</Button>
              <Button size="sm" className="bg-green-600 hover:bg-green-700">1</Button>
              <Button variant="outline" size="sm">2</Button>
              <Button variant="outline" size="sm">3</Button>
              <Button variant="outline" size="sm">下一页</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleGallery;
