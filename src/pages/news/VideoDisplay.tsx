import React, { useState, useMemo } from 'react';
import PageTemplate from '@/components/PageTemplate';
import CategoryTabs from '@/components/CategoryTabs';
import VideoPlayer from '@/components/VideoPlayer';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Play } from 'lucide-react';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import videosData from '@/data/videos.json';

const VideoDisplay: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 状态管理
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const videosPerPage = 6;

  // 获取分类和视频数据
  const { categories, videos } = videosData;

  // 根据选中分类筛选视频
  const filteredVideos = useMemo(() => {
    if (selectedCategory === 'all') {
      return videos;
    }
    return videos.filter(video => video.category === selectedCategory);
  }, [videos, selectedCategory]);

  // 分页逻辑
  const totalPages = Math.ceil(filteredVideos.length / videosPerPage);
  const startIndex = (currentPage - 1) * videosPerPage;
  const currentVideos = filteredVideos.slice(startIndex, startIndex + videosPerPage);

  // 处理分类变化
  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category.id);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <PageTemplate
      title="视频展示"
      subtitle="通过视频了解医院风貌和中医诊疗特色"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "视频展示",
        description: "直观展示医院风貌和中医诊疗特色",
        backgroundImage: "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 分类选择器 */}
      <CategoryTabs
        categories={categories}
        theme="red"
        activeCategory={selectedCategory}
        showAllOption={true}
        onCategoryChange={handleCategoryChange}
        className="mb-8"
      />

      {/* 视频展示区域 */}
      <section className="mb-8">
        {currentVideos.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentVideos.map((video) => (
              <div key={video.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 group">
                <div className="relative">
                  <VideoPlayer
                    sources={[{
                      src: video.videoSrc,
                      type: "video/mp4"
                    }]}
                    poster={video.poster}
                    controls={true}
                    autoPlay={false}
                    muted={true}
                    aspectRatio="video"
                    size="md"
                    className="w-full"
                  />

                  {/* 视频时长标签 */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs font-medium">
                    <Play className="w-3 h-3 inline mr-1" />
                    {video.duration}
                  </div>
                </div>

                {/* 视频信息 */}
                <div className="p-4">
                  <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                    {video.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>发布时间: {video.publishDate}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <p className="text-lg">该分类下暂无视频内容</p>
          </div>
        )}
      </section>

      {/* 分页组件 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 mb-8">
          {/* 上一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center space-x-1"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>上一页</span>
          </Button>

          {/* 页码按钮 */}
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, index) => {
              const page = index + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className={`w-8 h-8 p-0 ${
                    currentPage === page
                      ? 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                      : 'text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </Button>
              );
            })}
          </div>

          {/* 下一页按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center space-x-1"
          >
            <span>下一页</span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* 统计信息 */}
      <div className="bg-gray-50 rounded-lg p-6 text-center">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{filteredVideos.length}</div>
            <div className="text-sm text-gray-600">当前分类视频数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{videos.length}</div>
            <div className="text-sm text-gray-600">视频总数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{totalPages}</div>
            <div className="text-sm text-gray-600">总页数</div>
          </div>
        </div>
      </div>
    </PageTemplate>
  );
};

export default VideoDisplay;
