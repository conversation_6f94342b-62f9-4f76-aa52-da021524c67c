import React from 'react';
import { useTranslation } from 'react-i18next';
import { useResponsiveLayout } from '@/hooks/use-responsive-layout';
import { getTextMetrics, estimateTextWidth } from '@/utils/layoutUtils';

const LayoutTest: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { layoutConfig, dynamicStyles, screenWidth, logoText, currentLanguage } = useResponsiveLayout();

  const languages = [
    { code: 'zh', name: '中文', logoText: '大连东海医院' },
    { code: 'en', name: 'English', logoText: 'Dalian Donghai Hospital' },
    { code: 'ru', name: 'Русский', logoText: 'Больница Далянь Дунхай' }
  ];

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          TopNavigation 多语言布局测试
        </h1>

        {/* 当前状态显示 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">当前布局状态</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><strong>当前语言:</strong> {currentLanguage}</p>
              <p><strong>Logo文本:</strong> {logoText}</p>
              <p><strong>屏幕宽度:</strong> {screenWidth}px</p>
              <p><strong>估算文本宽度:</strong> {estimateTextWidth(logoText, currentLanguage)}px</p>
            </div>
            <div>
              <p><strong>Logo最大宽度:</strong> {layoutConfig.logoMaxWidth}</p>
              <p><strong>搜索框可见:</strong> {layoutConfig.searchVisible ? '是' : '否'}</p>
              <p><strong>菜单按钮文本:</strong> {layoutConfig.menuButtonText ? '是' : '否'}</p>
              <p><strong>容器内边距:</strong> {layoutConfig.containerPadding}</p>
            </div>
          </div>
        </div>

        {/* 语言切换测试 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">语言切换测试</h2>
          <div className="flex flex-wrap gap-4 mb-4">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => changeLanguage(lang.code)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  currentLanguage === lang.code
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {lang.name}
              </button>
            ))}
          </div>
          
          {/* 各语言文本长度对比 */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 px-4 py-2">语言</th>
                  <th className="border border-gray-300 px-4 py-2">Logo文本</th>
                  <th className="border border-gray-300 px-4 py-2">字符数</th>
                  <th className="border border-gray-300 px-4 py-2">估算宽度</th>
                  <th className="border border-gray-300 px-4 py-2">布局配置</th>
                </tr>
              </thead>
              <tbody>
                {languages.map((lang) => {
                  const metrics = getTextMetrics(lang.logoText, lang.code);
                  const config = layoutConfig; // 当前配置
                  return (
                    <tr key={lang.code} className={currentLanguage === lang.code ? 'bg-red-50' : ''}>
                      <td className="border border-gray-300 px-4 py-2">{lang.name}</td>
                      <td className="border border-gray-300 px-4 py-2 font-mono">{lang.logoText}</td>
                      <td className="border border-gray-300 px-4 py-2">{metrics.textLength}</td>
                      <td className="border border-gray-300 px-4 py-2">{metrics.estimatedWidth}px</td>
                      <td className="border border-gray-300 px-4 py-2 text-sm">
                        {currentLanguage === lang.code ? (
                          <div>
                            <div>最大宽度: {config.logoMaxWidth}</div>
                            <div>搜索: {config.searchVisible ? '✓' : '✗'}</div>
                            <div>菜单文本: {config.menuButtonText ? '✓' : '✗'}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400">切换到此语言查看</span>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* 响应式测试说明 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">测试说明</h2>
          <div className="space-y-3 text-gray-700">
            <p>1. <strong>切换语言</strong>：点击上方按钮切换不同语言，观察TopNavigation的布局变化</p>
            <p>2. <strong>调整窗口大小</strong>：拖拽浏览器窗口边缘，测试响应式布局效果</p>
            <p>3. <strong>移动端测试</strong>：使用浏览器开发者工具切换到移动端视图</p>
            <p>4. <strong>观察要点</strong>：
              <span className="block ml-4 mt-2">
                • Logo文本是否被正确截断<br/>
                • 搜索框是否在合适的时机隐藏<br/>
                • 菜单按钮文本是否根据空间调整<br/>
                • MobileNavigationMenu是否正确定位
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LayoutTest;
