import React, { useState, useEffect } from 'react';
import PageTemplate from '@/components/PageTemplate';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import Breadcrumb from '@/components/Breadcrumb';
import CategoryTabs from '@/components/CategoryTabs';
import CardComponent from '@/components/CardComponent';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import internationalCooperationData from '@/data/international-cooperation.json';

const InternationalCooperation: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 状态管理
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 9;

  // 根据分类筛选文章
  const filteredArticles = selectedCategory === 'all'
    ? internationalCooperationData.articles
    : internationalCooperationData.articles.filter(article => article.categoryId === selectedCategory);

  // 分页处理
  const totalPages = Math.ceil(filteredArticles.length / itemsPerPage);
  const currentArticles = filteredArticles.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // 分类变更时重置页码
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory]);

  return (
    <PageTemplate
      title="国际合作"
      subtitle="拓展国际视野，共建全球医疗合作网络"
      showHero={true}
      hero={{
        title: "国际合作",
        description: "拓展国际视野，共建全球医疗合作网络",
        backgroundImage: "https://picsum.photos/1920/800?random=international",
        theme: "red",
        height: "md"
      }}
    >
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbs} />
      </div>

      {/* 分类标签 */}
      <div className="mb-8">
        <CategoryTabs
          categories={internationalCooperationData.categories}
          theme="red"
          activeCategory={selectedCategory}
          defaultCategory="all"
          showAllOption={false}
          onCategoryChange={(category) => setSelectedCategory(category.id as string)}
          spacing="normal"
          allowWrap={true}
          size="default"
          variant="outline"
        />
      </div>

      {/* 合作内容展示区域 - 优化移动端两列布局 */}
      <div className="px-3 sm:px-4 lg:px-0">
        <div className="international-cooperation-grid grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 max-w-7xl mx-auto">
          {currentArticles.map((article) => (
            <div key={article.id} className="w-full cooperation-card-compact">
              <CardComponent
                id={article.id}
                title={article.title}
                subtitle={article.excerpt}
                image={article.image}
                imageAlt={article.title}
                href={article.href}
                size="md"
                imageAspectRatio="video"
                showHoverEffect={true}
                showImageZoom={true}
                showShadow={true}
                borderRadius="lg"
                textAlign="left"
                textTheme="red"
                backgroundColor="white"
                className="h-full"
                textClassName="!p-3 !pb-2"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 分页控件 */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6 mb-8">
          <div className="flex items-center space-x-2">
            {/* 上一页 */}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
              className="flex items-center space-x-1"
            >
              <ChevronLeft className="w-4 h-4" />
              <span>上一页</span>
            </Button>

            {/* 页码 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-2 ${
                  currentPage === page
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {page}
              </Button>
            ))}

            {/* 下一页 */}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
              className="flex items-center space-x-1"
            >
              <span>下一页</span>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* 自定义样式优化 */}
      <style>{`
        /* 移动端两列布局优化 */
        @media (max-width: 640px) {
          .international-cooperation-grid {
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            padding: 0;
          }

          /* 移动端卡片尺寸调整 */
          .cooperation-card-compact .w-48 {
            width: 100% !important;
            height: auto !important;
            min-height: 200px;
          }

          /* 移动端图片比例调整 */
          .cooperation-card-compact .aspect-video {
            aspect-ratio: 4/3;
          }

          /* 移动端文字大小调整 */
          .cooperation-card-compact .text-base {
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
          }

          .cooperation-card-compact .p-4 {
            padding: 0.75rem !important;
          }
        }

        /* 平板端优化 */
        @media (min-width: 641px) and (max-width: 768px) {
          .international-cooperation-grid {
            gap: 1rem;
            justify-content: center;
          }
        }

        /* 减少卡片底部间距 */
        .cooperation-card-compact {
          margin-bottom: 0.5rem;
        }

        /* 确保卡片在容器中居中 */
        .international-cooperation-grid {
          justify-items: center;
        }
      `}</style>
    </PageTemplate>
  );
};

export default InternationalCooperation;
