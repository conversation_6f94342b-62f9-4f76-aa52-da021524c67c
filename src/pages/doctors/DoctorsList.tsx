import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import CardComponent from '@/components/CardComponent';
import HorizontalScrollContainer from '@/components/HorizontalScrollContainer';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import doctorsData from '@/data/doctors.json';
import departmentsData from '@/data/departments.json';

const DoctorsList: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 获取医生和科室数据
  const { doctors } = doctorsData;
  const { departments } = departmentsData;

  return (
    <PageTemplate
      title="名医专家"
      subtitle="汇聚中医名家，为您的健康保驾护航"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "名医专家",
        description: "汇聚中医名家，传承医者仁心",
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 专家团队部分 */}
      <section className="my-24">
        {/* 专家团队标题 - 带装饰线 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="专家团队"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>

        {/* 专家团队卡片 - 水平滚动 */}
        <HorizontalScrollContainer
          showArrows={true}
          arrowTheme="red"
          responsive={{
            desktop: true,
            tablet: true,
            mobile: true
          }}
          gridConfig={{
            cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
            gap: 'gap-6'
          }}
          scrollConfig={{
            gap: 'gap-6',
            minWidth: 'min-w-[280px]',
            centerAlign: true
          }}
        >
          {doctors.map((doctor) => (
            <CardComponent
              key={doctor.id}
              id={doctor.id}
              image={doctor.image}
              imageAlt={doctor.name}
              title={doctor.name}
              subtitle={doctor.title}
              href={doctor.href}
              size="md"
              imageAspectRatio="square"
              showHoverEffect={true}
              showImageZoom={true}
              showShadow={true}
              borderRadius="lg"
              textAlign="center"
              textTheme="red"
              backgroundColor="white"
            />
          ))}
        </HorizontalScrollContainer>
      </section>

      {/* 科室导航部分 */}
      <section className="my-24">
        {/* 科室导航标题 - 带装饰线 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="科室导航"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>

        {/* 科室导航卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {departments.map((department) => (
            <CardComponent
              key={department.id}
              id={department.id}
              image={department.image}
              imageAlt={department.name}
              title={department.name}
              subtitle={department.description}
              href={department.href}
              size="xs"
              imageAspectRatio="video"
              showHoverEffect={true}
              showImageZoom={true}
              showShadow={true}
              borderRadius="lg"
              textAlign="center"
              textTheme="red"
              backgroundColor="white"
            />
          ))}
        </div>
      </section>

    </PageTemplate>
  );
};

export default DoctorsList;
