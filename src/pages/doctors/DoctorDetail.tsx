import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PageTemplate from '@/components/PageTemplate';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, Clock, MapPin, Phone, Award, BookOpen } from 'lucide-react';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';

const DoctorDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 模拟医生详细信息数据
  const doctorData = {
    id: id || '1',
    name: '张明华',
    title: '主任医师',
    department: '中医内科',
    photo: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    specialties: ['中医内科', '心血管疾病', '消化系统疾病', '中医养生'],
    education: '北京中医药大学博士',
    experience: '从医30年',
    introduction: '张明华主任医师是我院中医内科的资深专家，拥有30年丰富的临床经验。擅长运用中医理论诊治各种内科疾病，特别是在心血管疾病、消化系统疾病方面有独到的见解和显著的疗效。',
    achievements: [
      '国家级名中医',
      '中华中医药学会理事',
      '发表学术论文50余篇',
      '主编中医专著3部'
    ],
    schedule: [
      { day: '周一', time: '上午 8:00-12:00', status: '可预约' },
      { day: '周三', time: '上午 8:00-12:00', status: '可预约' },
      { day: '周五', time: '下午 14:00-17:00', status: '可预约' }
    ],
    consultationFee: '专家号 ¥50',
    location: '门诊楼3楼中医内科诊室'
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleAppointment = () => {
    // 预约功能
    alert('预约功能开发中...');
  };

  return (
    <PageTemplate
      title={`${doctorData.name} - 医生详情`}
      subtitle="专家医生详细信息"
      breadcrumbs={breadcrumbs}
      showHero={false}
    >
      {/* 返回按钮 */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>返回</span>
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* 医生基本信息 */}
        <div className="p-6 lg:p-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* 医生照片 */}
            <div className="flex-shrink-0">
              <img
                src={doctorData.photo}
                alt={doctorData.name}
                className="w-48 h-48 lg:w-64 lg:h-64 rounded-lg object-cover mx-auto lg:mx-0"
              />
            </div>

            {/* 医生信息 */}
            <div className="flex-1">
              <div className="text-center lg:text-left">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                  {doctorData.name}
                </h1>
                <p className="text-xl text-red-600 mb-4">
                  {doctorData.title} | {doctorData.department}
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center justify-center lg:justify-start">
                    <BookOpen className="w-5 h-5 text-gray-500 mr-2" />
                    <span className="text-gray-600">{doctorData.education}</span>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start">
                    <Clock className="w-5 h-5 text-gray-500 mr-2" />
                    <span className="text-gray-600">{doctorData.experience}</span>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start">
                    <MapPin className="w-5 h-5 text-gray-500 mr-2" />
                    <span className="text-gray-600">{doctorData.location}</span>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start">
                    <Phone className="w-5 h-5 text-gray-500 mr-2" />
                    <span className="text-gray-600">{doctorData.consultationFee}</span>
                  </div>
                </div>

                {/* 专长领域 */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">专长领域</h3>
                  <div className="flex flex-wrap gap-2 justify-center lg:justify-start">
                    {doctorData.specialties.map((specialty, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 预约按钮 */}
                <div className="flex justify-center lg:justify-start">
                  <Button
                    onClick={handleAppointment}
                    className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg"
                  >
                    <Calendar className="w-5 h-5 mr-2" />
                    立即预约
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 详细信息区域 */}
        <div className="border-t border-gray-200">
          <div className="p-6 lg:p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 医生简介 */}
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <BookOpen className="w-5 h-5 mr-2 text-red-600" />
                  医生简介
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {doctorData.introduction}
                </p>
              </div>

              {/* 学术成就 */}
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                  <Award className="w-5 h-5 mr-2 text-red-600" />
                  学术成就
                </h3>
                <ul className="space-y-2">
                  {doctorData.achievements.map((achievement, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <div className="w-2 h-2 bg-red-600 rounded-full mr-3"></div>
                      {achievement}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 出诊时间 */}
        <div className="border-t border-gray-200">
          <div className="p-6 lg:p-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-red-600" />
              出诊时间
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {doctorData.schedule.map((schedule, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-4 text-center"
                >
                  <div className="font-semibold text-gray-800 mb-2">
                    {schedule.day}
                  </div>
                  <div className="text-gray-600 mb-2">
                    {schedule.time}
                  </div>
                  <div className={`text-sm px-2 py-1 rounded ${
                    schedule.status === '可预约' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {schedule.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PageTemplate>
  );
};

export default DoctorDetail;
