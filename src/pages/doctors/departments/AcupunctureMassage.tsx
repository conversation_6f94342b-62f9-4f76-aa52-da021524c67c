import React from 'react';
import PageTemplate from '@/components/PageTemplate';
import Title from '@/components/Title';
import CardComponent from '@/components/CardComponent';
import ArticlesList from '@/components/ArticlesList';
import { getBreadcrumbs } from '@/routes';
import { useLocation } from 'react-router-dom';
import departmentData from '@/data/departments-detail.json';

const AcupunctureMassage: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 获取科室数据
  const department = departmentData.departments['acupuncture-massage'];

  // 构建科室介绍文章数据
  const introductionArticle = {
    id: 'acupuncture-massage-intro',
    title: department.introduction.title,
    excerpt: department.introduction.content,
    image: department.introduction.image,
    category: '科室介绍',
    date: '',
    href: '#'
  };

  return (
    <PageTemplate
      title={department.name}
      subtitle={department.description}
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: department.name,
        description: department.description,
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 科室介绍区域 - 使用 ArticlesList 组件 */}
      <section className="mb-12">
        <ArticlesList
          articles={[introductionArticle]}
          showImage={true}
          showCategory={false}
          showDate={false}
          showViews={false}
          showReadMore={false}
          imageSize="lg"
          theme="red"
          spacing="normal"
          showHoverEffect={false}
        />
      </section>

      {/* 科室医生展示区域 */}
      <section className="mb-12">
        {/* 科室医生标题 - 使用 Title 组件 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-red-300"></div>
          <Title
            title="科室医生"
            size="sm"
            align="center"
            className="mx-8"
          />
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-red-300"></div>
        </div>

        {/* 医生卡片网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {department.doctors.map((doctor) => (
            <CardComponent
              key={doctor.id}
              id={doctor.id.toString()}
              image={doctor.image}
              imageAlt={doctor.name}
              title={doctor.name}
              subtitle={doctor.title}
              href={doctor.href}
              size="md"
              imageAspectRatio="square"
              showHoverEffect={true}
              showImageZoom={true}
              showShadow={true}
              borderRadius="lg"
              textAlign="center"
              textTheme="red"
              backgroundColor="white"
            />
          ))}
        </div>
      </section>
    </PageTemplate>
  );
};

export default AcupunctureMassage;
