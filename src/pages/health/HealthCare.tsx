import React, { useState, useMemo } from 'react';
import PageTemplate from '@/components/PageTemplate';
import CategoryTabs from '@/components/CategoryTabs';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Users, CheckCircle, MessageCircle, Info } from 'lucide-react';
import { getBreadcrumbs } from '@/routes';
import { useLocation, useNavigate } from 'react-router-dom';
import healthCareData from '@/data/health-care.json';

const HealthCare: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 状态管理
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const servicesPerPage = 4;

  // 获取数据
  const { categories, services } = healthCareData;

  // 根据选中分类筛选服务
  const filteredServices = useMemo(() => {
    if (selectedCategory === 'all') {
      return services;
    }
    return services.filter(service => service.category === selectedCategory);
  }, [services, selectedCategory]);

  // 分页逻辑
  const totalPages = Math.ceil(filteredServices.length / servicesPerPage);
  const startIndex = (currentPage - 1) * servicesPerPage;
  const currentServices = filteredServices.slice(startIndex, startIndex + servicesPerPage);

  // 处理分类变化
  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category.id);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <PageTemplate
      title="健康管理"
      subtitle="专业的健康管理服务"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "健康管理",
        description: "专业的健康管理服务",
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 分类标签区域 */}
      <CategoryTabs
        categories={categories}
        theme="red"
        activeCategory={selectedCategory}
        showAllOption={true}
        onCategoryChange={handleCategoryChange}
        className="mb-8"
      />

      {/* 健康管理服务展示区域 */}
      <section className="mb-12">
        <div className="space-y-6">
          {currentServices.map((service) => (
            <div key={service.id} className="bg-white rounded-lg shadow-md border overflow-hidden hover:shadow-lg transition-shadow duration-200">
              <div className="flex flex-col lg:flex-row">
                {/* 图片 */}
                <div className="w-full lg:w-80 h-64 lg:h-auto flex-shrink-0">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>

                {/* 内容 */}
                <div className="flex-1 p-6 lg:p-8">
                  <div className="flex flex-col h-full">
                    {/* 标题 */}
                    <div className="mb-4">
                      <h2 className="text-xl lg:text-2xl font-bold text-gray-800">
                        {service.title}
                      </h2>
                    </div>

                    {/* 描述 */}
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {service.excerpt}
                    </p>

                    {/* 服务信息 */}
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2 text-red-500" />
                        <span>{service.targetGroup}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-2 text-red-500" />
                        <span>{service.duration}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-2 text-red-500" />
                        <span>随时咨询</span>
                      </div>
                    </div>

                    {/* 特色标签 */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {service.features?.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {feature}
                        </span>
                      ))}
                    </div>

                    {/* 按钮组 */}
                    <div className="mt-auto flex flex-col sm:flex-row gap-3">
                      <Button
                        className="flex-1 sm:flex-none bg-red-600 hover:bg-red-700 text-white px-6 py-2"
                        onClick={() => navigate('/email')}
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        立即咨询
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1 sm:flex-none border-red-600 text-red-600 hover:bg-red-50 px-6 py-2"
                        onClick={() => navigate('/email')}
                      >
                        <Info className="w-4 h-4 mr-2" />
                        了解详情
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              {/* 上一页 */}
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
                className="px-3 py-2"
              >
                上一页
              </Button>

              {/* 页码 */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-2 ${
                    currentPage === page
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </Button>
              ))}

              {/* 下一页 */}
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
                className="px-3 py-2"
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </section>
    </PageTemplate>
  );
};

export default HealthCare;
