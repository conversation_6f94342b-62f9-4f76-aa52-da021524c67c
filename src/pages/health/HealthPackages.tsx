import React, { useState, useMemo } from 'react';
import PageTemplate from '@/components/PageTemplate';
import CategoryTabs from '@/components/CategoryTabs';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Users, CheckCircle } from 'lucide-react';
import { getBreadcrumbs } from '@/routes';
import { useLocation, useNavigate } from 'react-router-dom';
import healthPackagesData from '@/data/health-packages.json';

const HealthPackages: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // 状态管理
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const packagesPerPage = 4;

  // 获取数据
  const { categories, packages } = healthPackagesData;

  // 根据选中分类筛选套餐
  const filteredPackages = useMemo(() => {
    if (selectedCategory === 'all') {
      return packages;
    }
    return packages.filter(pkg => pkg.category === selectedCategory);
  }, [packages, selectedCategory]);

  // 分页逻辑
  const totalPages = Math.ceil(filteredPackages.length / packagesPerPage);
  const startIndex = (currentPage - 1) * packagesPerPage;
  const currentPackages = filteredPackages.slice(startIndex, startIndex + packagesPerPage);

  // 处理分类变化
  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category.id);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 转换套餐数据为ArticlesList所需格式
  const articlesData = currentPackages.map(pkg => ({
    id: pkg.id,
    title: pkg.title,
    excerpt: pkg.excerpt,
    image: pkg.image,
    category: pkg.category,
    date: pkg.date,
    views: pkg.views,
    href: pkg.href,
    // 自定义字段用于套餐信息
    price: pkg.price,
    originalPrice: pkg.originalPrice,
    targetGroup: pkg.targetGroup,
    duration: pkg.duration,
    includes: pkg.includes,
    features: pkg.features,
    bookingUrl: pkg.bookingUrl
  }));

  return (
    <PageTemplate
      title="健康套餐"
      subtitle="个性化健康管理方案"
      breadcrumbs={breadcrumbs}
      showHero={true}
      hero={{
        title: "健康套餐",
        description: "个性化健康管理方案",
        backgroundImage: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
        theme: "red",
        height: "md"
      }}
    >
      {/* 分类标签区域 */}
      <CategoryTabs
        categories={categories}
        theme="red"
        activeCategory={selectedCategory}
        showAllOption={true}
        onCategoryChange={handleCategoryChange}
        className="mb-8"
      />

      {/* 套餐展示区域 */}
      <section className="mb-12">
        <div className="space-y-6">
          {articlesData.map((pkg) => (
            <div key={pkg.id} className="bg-white rounded-lg shadow-md border overflow-hidden hover:shadow-lg transition-shadow duration-200">
              <div className="flex flex-col lg:flex-row">
                {/* 图片 */}
                <div className="w-full lg:w-80 h-64 lg:h-auto flex-shrink-0">
                  <img
                    src={pkg.image}
                    alt={pkg.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>

                {/* 内容 */}
                <div className="flex-1 p-6 lg:p-8">
                  <div className="flex flex-col h-full">
                    {/* 标题 */}
                    <div className="mb-4">
                      <h2 className="text-xl lg:text-2xl font-bold text-gray-800">
                        {pkg.title}
                      </h2>
                    </div>

                    {/* 描述 */}
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {pkg.excerpt}
                    </p>

                    {/* 套餐信息 */}
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2 text-red-500" />
                        <span>{pkg.targetGroup}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-2 text-red-500" />
                        <span>{pkg.duration}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-2 text-red-500" />
                        <span>随时预约</span>
                      </div>
                    </div>

                    {/* 特色标签 */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {pkg.features?.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {feature}
                        </span>
                      ))}
                    </div>

                    {/* 立即预约按钮 */}
                    <div className="mt-auto">
                      <Button
                        className="w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white px-8 py-2"
                        onClick={() => navigate('/email')}
                      >
                        立即预约
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              {/* 上一页 */}
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
                className="px-3 py-2"
              >
                上一页
              </Button>

              {/* 页码 */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-2 ${
                    currentPage === page
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </Button>
              ))}

              {/* 下一页 */}
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
                className="px-3 py-2"
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </section>
    </PageTemplate>
  );
};

export default HealthPackages;
