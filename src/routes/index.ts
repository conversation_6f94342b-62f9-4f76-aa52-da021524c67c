// 路由配置文件
import { lazy } from 'react';
import { BreadcrumbItem } from '@/components/Breadcrumb';

// 懒加载页面组件 - 导入所有App.tsx中使用的组件
const Home = lazy(() => import('@/pages/Home'));
const NotFound = lazy(() => import('@/pages/NotFound'));

// 医院介绍模块
const HospitalIntroduction = lazy(() => import('@/pages/hospital/HospitalIntroduction'));
const HospitalOverview = lazy(() => import('@/pages/hospital/HospitalOverview'));
const HospitalCulture = lazy(() => import('@/pages/hospital/HospitalCulture'));
const HospitalHistory = lazy(() => import('@/pages/hospital/HospitalHistory'));
const SocialCooperation = lazy(() => import('@/pages/hospital/SocialCooperation'));

// 新闻中心模块
const ArticleList = lazy(() => import('@/pages/news/ArticleList'));
const ArticleDetail = lazy(() => import('@/pages/news/ArticleDetail'));
const ArticleGallery = lazy(() => import('@/pages/news/ArticleGallery'));
const HospitalStyle = lazy(() => import('@/pages/news/HospitalStyle'));
const VideoDisplay = lazy(() => import('@/pages/news/VideoDisplay'));

// 专家医生模块
const DoctorsList = lazy(() => import('@/pages/doctors/DoctorsList'));
const DoctorDetail = lazy(() => import('@/pages/doctors/DoctorDetail'));
const InternalMedicine = lazy(() => import('@/pages/doctors/departments/InternalMedicine'));
const Surgery = lazy(() => import('@/pages/doctors/departments/Surgery'));
const Dermatology = lazy(() => import('@/pages/doctors/departments/Dermatology'));
const Gynecology = lazy(() => import('@/pages/doctors/departments/Gynecology'));
const Pediatrics = lazy(() => import('@/pages/doctors/departments/Pediatrics'));
const EntOphthalmology = lazy(() => import('@/pages/doctors/departments/EntOphthalmology'));
const AcupunctureMassage = lazy(() => import('@/pages/doctors/departments/AcupunctureMassage'));
const Ultrasound = lazy(() => import('@/pages/doctors/departments/Ultrasound'));
const Proctology = lazy(() => import('@/pages/doctors/departments/Proctology'));
const TcmRehabilitation = lazy(() => import('@/pages/doctors/departments/TcmRehabilitation'));

// 健康管理模块
const HealthPackages = lazy(() => import('@/pages/health/HealthPackages'));
const HealthCare = lazy(() => import('@/pages/health/HealthCare'));

// 科普疗法模块
const TcmScience = lazy(() => import('@/pages/therapy/TcmScience'));
const SpecialTherapy = lazy(() => import('@/pages/therapy/SpecialTherapy'));

// 特殊页面模块
const ExportBase = lazy(() => import('@/pages/special/ExportBase'));
const EducationBase = lazy(() => import('@/pages/special/EducationBase'));

// 合作案例模块
const DomesticCooperation = lazy(() => import('@/pages/cooperation/DomesticCooperation'));
const InternationalCooperation = lazy(() => import('@/pages/cooperation/InternationalCooperation'));

// 联系我们模块
const ContactUs = lazy(() => import('@/pages/contact/ContactUs'));
const EmailUs = lazy(() => import('@/pages/contact/EmailUs'));

// 路由配置
export interface RouteConfig {
  path: string;
  component: React.LazyExoticComponent<React.ComponentType<any>>;
  title: string;
  description?: string;
}

export const routes: RouteConfig[] = [
  // 基础页面
  { path: '/', component: Home, title: '首页' },

  // 医院介绍模块
  { path: '/hospital', component: HospitalIntroduction, title: '医院简介' },
  { path: '/hospital/overview', component: HospitalOverview, title: '医院概况' },
  { path: '/hospital/culture', component: HospitalCulture, title: '医院文化' },
  { path: '/hospital/history', component: HospitalHistory, title: '医院历程' },
  { path: '/hospital/social-cooperation', component: SocialCooperation, title: '社会合作' },

  // 新闻中心模块
  { path: '/articles', component: ArticleList, title: '医院新闻' },
  { path: '/news', component: ArticleList, title: '医院新闻' },
  { path: '/article/:id', component: ArticleDetail, title: '新闻详情' },
  { path: '/gallery', component: ArticleGallery, title: '医院风采' },
  { path: '/news/gallery', component: HospitalStyle, title: '医院风采' },
  { path: '/news/videos', component: VideoDisplay, title: '视频展示' },

  // 专家医生模块
  { path: '/doctors', component: DoctorsList, title: '专家医生' },
  { path: '/doctor/:id', component: DoctorDetail, title: '专家详情' },
  { path: '/doctors/internal-medicine', component: InternalMedicine, title: '内科' },
  { path: '/doctors/surgery', component: Surgery, title: '外科' },
  { path: '/doctors/dermatology', component: Dermatology, title: '皮肤科' },
  { path: '/doctors/gynecology', component: Gynecology, title: '妇科' },
  { path: '/doctors/pediatrics', component: Pediatrics, title: '儿科' },
  { path: '/doctors/ent-ophthalmology', component: EntOphthalmology, title: '五官科' },
  { path: '/doctors/acupuncture-massage', component: AcupunctureMassage, title: '针灸推拿科' },
  { path: '/doctors/ultrasound', component: Ultrasound, title: '超声科' },
  { path: '/doctors/proctology', component: Proctology, title: '肛肠科' },
  { path: '/doctors/tcm-rehabilitation', component: TcmRehabilitation, title: '中医康复科' },

  // 健康管理模块
  { path: '/health/packages', component: HealthPackages, title: '健康套餐' },
  { path: '/health/care', component: HealthCare, title: '健康管理' },

  // 科普疗法模块
  { path: '/therapy/tcm-science', component: TcmScience, title: '中医科普' },
  { path: '/therapy/special', component: SpecialTherapy, title: '特色疗法' },
  { path: '/therapy/special-therapy', component: SpecialTherapy, title: '特色疗法' },

  // 特殊页面模块
  { path: '/special/export-base', component: ExportBase, title: '国家中医药服务出口基地' },
  { path: '/special/education-base', component: EducationBase, title: '全国中医药文化宣传教育基地' },

  // 合作案例模块
  { path: '/cooperation', component: DomesticCooperation, title: '合作案例' },
  { path: '/cooperation/domestic', component: DomesticCooperation, title: '国内合作' },
  { path: '/cooperation/international', component: InternationalCooperation, title: '国际合作' },

  // 联系我们模块
  { path: '/contact', component: ContactUs, title: '联系我们' },
  { path: '/contact/info', component: ContactUs, title: '联系方式' },
  { path: '/email', component: EmailUs, title: '在线留言' },
  { path: '/contact/email', component: EmailUs, title: '在线留言' },

  // 404页面
  { path: '*', component: NotFound, title: '页面未找到' }
];

// 根据路径获取路由配置
export const getRouteByPath = (path: string): RouteConfig | undefined => {
  return routes.find(route => route.path === path);
};

// 导航层级映射，用于生成正确的面包屑导航
const navigationHierarchy: { [key: string]: { parent?: string; label: string } } = {
  '/hospital': { label: '医院简介' },
  '/hospital/overview': { parent: '/hospital', label: '医院概览' },
  '/hospital/culture': { parent: '/hospital', label: '医院文化' },
  '/hospital/history': { parent: '/hospital', label: '医院历程' },
  '/hospital/social-cooperation': { parent: '/hospital', label: '社会合作' },
  '/news': { label: '新闻中心' },
  '/articles': { parent: '/news', label: '医院新闻' },
  '/news/gallery': { parent: '/news', label: '医院风采' },
  '/news/videos': { parent: '/news', label: '视频展示' },
  '/doctors': { label: '名医专家' },
  '/doctors/internal-medicine': { parent: '/doctors', label: '内科' },
  '/doctors/surgery': { parent: '/doctors', label: '外科' },
  '/doctors/dermatology': { parent: '/doctors', label: '皮肤科' },
  '/doctors/gynecology': { parent: '/doctors', label: '妇科' },
  '/doctors/pediatrics': { parent: '/doctors', label: '儿科' },
  '/doctors/ent-ophthalmology': { parent: '/doctors', label: '眼耳鼻喉科' },
  '/doctors/acupuncture-massage': { parent: '/doctors', label: '针灸推拿' },
  '/doctors/ultrasound': { parent: '/doctors', label: '彩超' },
  '/doctors/proctology': { parent: '/doctors', label: '肛肠科' },
  '/doctors/tcm-rehabilitation': { parent: '/doctors', label: '中医康复科' },
  '/health': { label: '健康管理（体检）中心' },
  '/health/packages': { parent: '/health', label: '体检套餐' },
  '/health/care': { parent: '/health', label: '健康管理' },
  '/therapy': { label: '科普与疗法' },
  '/therapy/tcm-science': { parent: '/therapy', label: '中医药科普' },
  '/therapy/special': { parent: '/therapy', label: '特色疗法' },
  '/special/export-base': { label: '国家中医药服务出口基地' },
  '/special/education-base': { label: '全国中医药文化宣传教育基地' },
  '/cooperation': { label: '合作案例' },
  '/cooperation/domestic': { parent: '/cooperation', label: '国内合作' },
  '/cooperation/international': { parent: '/cooperation', label: '国际合作' },
  '/contact': { label: '联系我们' },
  '/contact/email': { parent: '/contact', label: '在线留言' },
  '/email': { parent: '/contact', label: '在线留言' },
};

// 获取面包屑导航
export const getBreadcrumbs = (pathname: string): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [{ label: '首页', href: '/' }];

  // 构建路径层级
  const buildHierarchy = (path: string): string[] => {
    const hierarchy: string[] = [];
    let currentPath = path;

    while (currentPath && navigationHierarchy[currentPath]) {
      hierarchy.unshift(currentPath);
      currentPath = navigationHierarchy[currentPath].parent || '';
    }

    return hierarchy;
  };

  const hierarchy = buildHierarchy(pathname);

  hierarchy.forEach((path, index) => {
    const navItem = navigationHierarchy[path];
    if (navItem) {
      breadcrumbs.push({
        label: navItem.label,
        href: path,
        active: index === hierarchy.length - 1
      });
    }
  });

  return breadcrumbs;
};
