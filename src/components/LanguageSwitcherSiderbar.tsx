import { useTranslation } from 'react-i18next';

const languages = [
    { code: 'zh', label: '中文' },
    { code: 'en', label: 'English' },
    { code: 'ru', label: 'Русский' },
  ];

const LanguageSwitcherSidebar = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="flex flex-wrap gap-2">
      {languages.map((lang) => (
        <button
          key={lang.code}
          onClick={() => changeLanguage(lang.code)}
          className={`py-1.5 px-3 rounded-md text-sm font-medium transition-all duration-200 border min-w-0 flex-shrink-0 ${
            i18n.language === lang.code
              ? 'bg-red-600 text-white border-red-600 shadow-sm'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
          }`}
          title={lang.label}
        >
          <span className="truncate max-w-[80px] block">{lang.label}</span>
        </button>
      ))}
    </div>
  );
};

export default LanguageSwitcherSidebar;
