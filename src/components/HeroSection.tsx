import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, LucideIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export interface HeroSectionProps {
  /** 主标题 */
  title: string;
  /** 副标题/描述 */
  description?: string;
  /** 背景图片URL */
  backgroundImage?: string;
  /** 背景渐变颜色主题 */
  theme?: string;
  /** 高度设置 */
  height?: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
  /** 按钮配置 */
  button?: {
    text: string;
    onClick?: () => void;
    href?: string;
    icon?: LucideIcon;
    variant?: string;
  };
  /** 文本对齐方式 */
  textAlign?: 'left' | 'center' | 'right';
  /** 是否显示背景图片遮罩 */
  showOverlay?: boolean;
  /** 背景图片透明度 */
  backgroundOpacity?: number;
  /** 自定义CSS类名 */
  className?: string;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  description,
  backgroundImage,
  theme = 'red',
  height = 'lg',
  button,
  textAlign = 'center',
  showOverlay = true,
  backgroundOpacity = 20,
  className = '',
  enableI18n = false,
  i18nPrefix = 'hero'
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // 主题颜色映射
  const themeClasses: Record<string, string> = {
    red: 'from-red-600 to-red-700',
    blue: 'from-blue-600 to-blue-700',
    green: 'from-green-600 to-green-700',
    purple: 'from-purple-600 to-purple-700',
    gray: 'from-gray-600 to-gray-700'
  };

  // 获取安全的主题类名
  const getThemeClass = (themeKey?: string) => {
    return themeClasses[themeKey || 'red'] || themeClasses.red;
  };

  // 高度映射
  const heightClasses = {
    sm: 'h-32 sm:h-40 md:h-48',
    md: 'h-48 sm:h-56 md:h-64',
    lg: 'h-48 sm:h-64 md:h-80 lg:h-96',
    xl: 'h-64 sm:h-80 md:h-96 lg:h-[32rem]',
    auto: 'min-h-[12rem] py-12 sm:py-16 md:py-20'
  };

  // 文本对齐映射
  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (text: string, key: string) => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.${key}`, text);
    }
    return text;
  };

  // 处理按钮点击
  const handleButtonClick = () => {
    if (button?.onClick) {
      button.onClick();
    } else if (button?.href) {
      // 检查是否是外部链接
      if (button.href.startsWith('http://') || button.href.startsWith('https://')) {
        window.open(button.href, '_blank');
      } else {
        // 使用React Router进行内部导航
        navigate(button.href);
      }
    }
  };

  // 按钮变体样式
  const getButtonVariant = () => {
    const currentTheme = theme || 'red';
    switch (button?.variant) {
      case 'primary':
        return `bg-${currentTheme}-600 text-white hover:bg-${currentTheme}-700`;
      case 'secondary':
        return 'bg-white text-gray-800 hover:bg-gray-100';
      case 'outline':
        return `border-white text-white hover:bg-white hover:text-${currentTheme}-600`;
      default:
        return 'bg-white text-gray-800 hover:bg-gray-100';
    }
  };

  const ButtonIcon = button?.icon || ArrowRight;

  return (
    <section
      className={`
        relative ${heightClasses[height || 'md']}
        bg-gradient-to-r ${getThemeClass(theme)}
        flex items-center justify-center text-white overflow-hidden
        ${className}
      `}
    >
      {/* 背景图片 */}
      {backgroundImage && (
        <div 
          className={`absolute inset-0 bg-cover bg-center ${showOverlay ? `opacity-${backgroundOpacity}` : ''}`}
          style={{
            backgroundImage: `url('${backgroundImage}')`
          }}
        />
      )}

      {/* 内容区域 */}
      <div className={`relative z-10 ${textAlignClasses[textAlign]} max-w-4xl mx-auto px-4 sm:px-6`}>
        {/* 主标题 */}
        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-2 sm:mb-4">
          {getDisplayText(title, 'title')}
        </h1>

        {/* 描述文本 */}
        {description && (
          <p className="text-sm sm:text-base md:text-lg lg:text-xl mb-4 sm:mb-6 md:mb-8 opacity-90">
            {getDisplayText(description, 'description')}
          </p>
        )}

        {/* 按钮 */}
        {button && (
          <Button 
            size="lg" 
            onClick={handleButtonClick}
            className={`
              ${getButtonVariant()}
              text-sm sm:text-base h-12 px-6
              transition-all duration-200
            `}
          >
            {getDisplayText(button.text, 'buttonText')}
            {button.icon && <ButtonIcon className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />}
          </Button>
        )}
      </div>
    </section>
  );
};

export default HeroSection;
