import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

export interface CategoryItem {
  /** 分类ID */
  id: string | number;
  /** 显示标签 */
  label: string;
  /** 分类值 */
  value?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 国际化键名 */
  i18nKey?: string;
  /** 自定义数据 */
  data?: any;
}

export interface CategoryTabsProps {
  /** 分类列表 */
  categories: CategoryItem[];
  /** 当前选中的分类ID */
  activeCategory?: string | number;
  /** 默认选中的分类ID */
  defaultCategory?: string | number;
  /** 分类变化回调 */
  onCategoryChange?: (category: CategoryItem, index: number) => void;
  /** 颜色主题 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 按钮尺寸 */
  size?: 'sm' | 'default' | 'lg';
  /** 按钮变体 */
  variant?: 'default' | 'outline' | 'ghost';
  /** 布局方式 */
  layout?: 'horizontal' | 'vertical';
  /** 是否允许换行 */
  allowWrap?: boolean;
  /** 间距大小 */
  spacing?: 'tight' | 'normal' | 'loose';
  /** 自定义CSS类名 */
  className?: string;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 是否显示全部选项 */
  showAllOption?: boolean;
  /** 全部选项的文本 */
  allOptionText?: string;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  categories,
  activeCategory,
  defaultCategory,
  onCategoryChange,
  theme = 'red',
  size = 'sm',
  variant = 'outline',
  layout = 'horizontal',
  allowWrap = true,
  spacing = 'normal',
  className = '',
  enableI18n = false,
  i18nPrefix = 'categories',
  showAllOption = true,
  allOptionText = '全部'
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string | number | null>(null);

  // 初始化选中状态
  useEffect(() => {
    if (activeCategory !== undefined) {
      setSelectedCategory(activeCategory);
    } else if (defaultCategory !== undefined) {
      setSelectedCategory(defaultCategory);
    } else if (showAllOption) {
      setSelectedCategory('all');
    } else if (categories.length > 0) {
      setSelectedCategory(categories[0].id);
    }
  }, [activeCategory, defaultCategory, categories, showAllOption]);

  // 当 activeCategory prop 变化时，同步内部状态
  useEffect(() => {
    if (activeCategory !== undefined) {
      setSelectedCategory(activeCategory);
    }
  }, [activeCategory]);

  // 主题颜色映射
  const themeClasses = {
    red: {
      active: 'bg-red-600 hover:bg-red-700 text-white border-red-600',
      inactive: 'text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
    },
    blue: {
      active: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600',
      inactive: 'text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
    },
    green: {
      active: 'bg-green-600 hover:bg-green-700 text-white border-green-600',
      inactive: 'text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
    },
    purple: {
      active: 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600',
      inactive: 'text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
    },
    gray: {
      active: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600',
      inactive: 'text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
    }
  };

  // 间距映射
  const spacingClasses = {
    tight: 'gap-1',
    normal: 'gap-2',
    loose: 'gap-4'
  };

  // 布局类名
  const layoutClasses = {
    horizontal: `flex ${allowWrap ? 'flex-wrap' : ''} ${spacingClasses[spacing]}`,
    vertical: `flex flex-col ${spacingClasses[spacing]}`
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (category: CategoryItem) => {
    if (enableI18n && category.i18nKey && i18nPrefix) {
      return t(`${i18nPrefix}.${category.i18nKey}`, category.label);
    }
    return category.label;
  };

  // 获取全部选项文本
  const getAllOptionText = () => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.all`, allOptionText);
    }
    return allOptionText;
  };

  // 处理分类点击
  const handleCategoryClick = (category: CategoryItem, index: number) => {
    if (category.disabled) return;
    
    setSelectedCategory(category.id);
    if (onCategoryChange) {
      onCategoryChange(category, index);
    }
  };

  // 处理全部选项点击
  const handleAllOptionClick = () => {
    const allCategory: CategoryItem = {
      id: 'all',
      label: getAllOptionText(),
      value: 'all'
    };
    setSelectedCategory('all');
    if (onCategoryChange) {
      onCategoryChange(allCategory, -1);
    }
  };

  // 获取按钮样式
  const getButtonClassName = (categoryId: string | number, disabled?: boolean) => {
    const isActive = selectedCategory === categoryId;
    const themeClass = themeClasses[theme];
    
    let className = '';
    
    if (disabled) {
      className = 'opacity-50 cursor-not-allowed';
    } else if (isActive) {
      className = themeClass.active;
    } else {
      className = themeClass.inactive;
    }

    return className;
  };

  // 准备渲染的分类列表
  const renderCategories = showAllOption 
    ? [{ id: 'all', label: getAllOptionText(), value: 'all' }, ...categories]
    : categories;

  return (
    <div className={`mb-6 sm:mb-8 ${className}`}>
      <div className={layoutClasses[layout]}>
        {renderCategories.map((category, index) => {
          const actualIndex = showAllOption ? index - 1 : index;
          const isAllOption = showAllOption && index === 0;
          
          return (
            <Button
              key={category.id}
              variant={variant}
              size={size}
              disabled={category.disabled}
              onClick={() => 
                isAllOption 
                  ? handleAllOptionClick() 
                  : handleCategoryClick(category, actualIndex)
              }
              className={`
                ${getButtonClassName(category.id, category.disabled)}
                text-xs sm:text-sm
                transition-all duration-200
                ${layout === 'vertical' ? 'justify-start' : ''}
              `}
            >
              {getDisplayText(category)}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default CategoryTabs;
