import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

interface DynamicTitleProps {
  /** 页面标题 */
  title?: string;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 国际化键 */
  i18nKey?: string;
}

const DynamicTitle: React.FC<DynamicTitleProps> = ({
  title,
  enableI18n = true,
  i18nPrefix = 'common',
  i18nKey = 'logo.main'
}) => {
  const { t } = useTranslation();
  const location = useLocation();

  useEffect(() => {
    // 获取医院名称
    const hospitalName = enableI18n 
      ? t(`${i18nPrefix}.${i18nKey}`, '大连东海医院')
      : '大连东海医院';

    // 如果有自定义标题，则组合显示
    const pageTitle = title 
      ? `${title} - ${hospitalName}`
      : hospitalName;

    // 更新页面标题
    document.title = pageTitle;

    // 更新meta标签
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      const description = enableI18n 
        ? t('common.description', '大连东海医院 - 致力于中国传统医学，以中医为主，中西医结合')
        : '大连东海医院 - 致力于中国传统医学，以中医为主，中西医结合';
      metaDescription.setAttribute('content', description);
    }

    // 更新Open Graph标签
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', pageTitle);
    }

    const ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      const description = enableI18n 
        ? t('common.description', '大连东海医院 - 致力于中国传统医学，以中医为主，中西医结合')
        : '大连东海医院 - 致力于中国传统医学，以中医为主，中西医结合';
      ogDescription.setAttribute('content', description);
    }

  }, [title, enableI18n, i18nPrefix, i18nKey, t, location.pathname]);

  // 这个组件不渲染任何内容
  return null;
};

export default DynamicTitle;
