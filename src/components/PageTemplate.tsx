import React from 'react';
import HeroSection from '@/components/HeroSection';
import Breadcrumb, { BreadcrumbItem } from '@/components/Breadcrumb';
import Title from '@/components/Title';
import { ArrowRight } from 'lucide-react';

export interface PageTemplateProps {
  /** 页面标题 */
  title: string;
  /** 页面副标题 */
  subtitle?: string;
  /** 面包屑导航 */
  breadcrumbs?: BreadcrumbItem[];
  /** 是否显示Hero区域 */
  showHero?: boolean;
  /** Hero区域配置 */
  hero?: {
    title: string;
    description: string;
    backgroundImage?: string;
    theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
    height?: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
    button?: {
      text: string;
      variant?: 'primary' | 'secondary';
      onClick?: () => void;
    };
  };
  /** 页面内容 */
  children: React.ReactNode;
  /** 自定义CSS类名 */
  className?: string;
  /** 内容区域最大宽度 */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
  /** 内容区域内边距 */
  padding?: 'sm' | 'md' | 'lg' | 'xl';
}

const PageTemplate: React.FC<PageTemplateProps> = ({
  title,
  subtitle,
  breadcrumbs,
  showHero = false,
  hero,
  children,
  className = '',
  maxWidth = '7xl',
  padding = 'lg'
}) => {
  // 最大宽度映射
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  // 内边距映射
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-4 sm:p-6 md:p-8 lg:p-12',
    xl: 'p-6 sm:p-8 md:p-12 lg:p-16'
  };

  return (
    <div className={`flex flex-col min-h-screen ${className}`}>
      {/* Hero Section */}
      {showHero && hero && (
        <HeroSection
          title={hero.title}
          description={hero.description}
          backgroundImage={hero.backgroundImage}
          theme={hero.theme || 'red'}
          height={hero.height || 'md'}
          button={hero.button ? {
            text: hero.button.text,
            variant: hero.button.variant || 'secondary',
            icon: ArrowRight,
            onClick: hero.button.onClick
          } : undefined}
        />
      )}

      {/* Main Content */}
      <div className={`flex-1 ${paddingClasses[padding]}`}>
        <div className={`${maxWidthClasses[maxWidth]} mx-auto`}>
          {/* Breadcrumb */}
          {breadcrumbs && breadcrumbs.length > 0 && (
            <div className="mb-6">
              <Breadcrumb items={breadcrumbs} />
            </div>
          )}

          {/* Page Title */}
          {!showHero && (
            <div className="mb-8">
              <Title 
                title={title}
                subtitle={subtitle}
                size="lg"
                align="center"
              />
            </div>
          )}

          {/* Page Content */}
          <div className="space-y-8">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageTemplate;
