import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight, Pause, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

export interface CarouselItem {
  /** 项目ID */
  id: string | number;
  /** 图片URL */
  image: string;
  /** 图片alt文本 */
  imageAlt?: string;
  /** 标题 */
  title: string;
  /** 描述文本 */
  description?: string;
  /** 按钮文本 */
  buttonText?: string;
  /** 按钮链接 */
  buttonHref?: string;
  /** 按钮点击事件 */
  buttonOnClick?: () => void;
  /** 自定义数据 */
  data?: any;
}

export interface CarouselProps {
  /** 轮播项目列表 */
  items: CarouselItem[];
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 自动播放间隔（毫秒） */
  autoPlayInterval?: number;
  /** 是否无限循环 */
  infinite?: boolean;
  /** 是否显示指示器 */
  showIndicators?: boolean;
  /** 是否显示导航箭头 */
  showArrows?: boolean;
  /** 是否显示播放/暂停按钮 */
  showPlayPause?: boolean;
  /** 布局方向 */
  layout?: 'horizontal' | 'vertical';
  /** 图片位置（水平布局时） */
  imagePosition?: 'left' | 'right';
  /** 图片宽度比例（水平布局时） */
  imageWidthRatio?: number;
  /** 高度设置 */
  height?: 'auto' | 'sm' | 'md' | 'lg' | 'xl';
  /** 动画效果 */
  animation?: 'slide' | 'fade' | 'none';
  /** 动画持续时间（毫秒） */
  animationDuration?: number;
  /** 颜色主题 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 自定义CSS类名 */
  className?: string;
  /** 图片区域自定义CSS类名 */
  imageClassName?: string;
  /** 文本区域自定义CSS类名 */
  textClassName?: string;
  /** 项目变化回调 */
  onItemChange?: (item: CarouselItem, index: number) => void;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
}

const Carousel: React.FC<CarouselProps> = ({
  items,
  autoPlay = true,
  autoPlayInterval = 10000,
  infinite = true,
  showIndicators = true,
  showArrows = true,
  showPlayPause = false,
  layout = 'horizontal',
  imagePosition = 'left',
  imageWidthRatio = 0.5,
  height = 'md',
  animation = 'slide',
  animationDuration = 500,
  theme = 'red',
  className = '',
  imageClassName = '',
  textClassName = '',
  onItemChange,
  enableI18n = false,
  i18nPrefix = 'carousel'
}) => {
  const { t } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 高度映射
  const heightClasses = {
    auto: 'h-auto',
    sm: 'h-48 sm:h-64',
    md: 'h-64 sm:h-80',
    lg: 'h-80 sm:h-96',
    xl: 'h-96 sm:h-[32rem]'
  };

  // 主题颜色映射
  const themeClasses = {
    red: {
      button: 'bg-red-600 hover:bg-red-700 text-white',
      indicator: 'bg-red-600',
      indicatorInactive: 'bg-white bg-opacity-50'
    },
    blue: {
      button: 'bg-blue-600 hover:bg-blue-700 text-white',
      indicator: 'bg-blue-600',
      indicatorInactive: 'bg-white bg-opacity-50'
    },
    green: {
      button: 'bg-green-600 hover:bg-green-700 text-white',
      indicator: 'bg-green-600',
      indicatorInactive: 'bg-white bg-opacity-50'
    },
    purple: {
      button: 'bg-purple-600 hover:bg-purple-700 text-white',
      indicator: 'bg-purple-600',
      indicatorInactive: 'bg-white bg-opacity-50'
    },
    gray: {
      button: 'bg-gray-600 hover:bg-gray-700 text-white',
      indicator: 'bg-gray-600',
      indicatorInactive: 'bg-white bg-opacity-50'
    }
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (item: CarouselItem, key: string, fallback: string) => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.${key}`, fallback);
    }
    return fallback;
  };

  // 切换到指定索引
  const goToSlide = useCallback((index: number) => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setCurrentIndex(index);
    
    if (onItemChange && items[index]) {
      onItemChange(items[index], index);
    }

    setTimeout(() => {
      setIsTransitioning(false);
    }, animationDuration);
  }, [isTransitioning, onItemChange, items, animationDuration]);

  // 下一张
  const nextSlide = useCallback(() => {
    if (items.length === 0) return;
    
    const nextIndex = infinite 
      ? (currentIndex + 1) % items.length
      : Math.min(currentIndex + 1, items.length - 1);
    
    goToSlide(nextIndex);
  }, [currentIndex, items.length, infinite, goToSlide]);

  // 上一张
  const prevSlide = useCallback(() => {
    if (items.length === 0) return;
    
    const prevIndex = infinite
      ? currentIndex === 0 ? items.length - 1 : currentIndex - 1
      : Math.max(currentIndex - 1, 0);
    
    goToSlide(prevIndex);
  }, [currentIndex, items.length, infinite, goToSlide]);

  // 播放/暂停切换
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // 自动播放逻辑
  useEffect(() => {
    if (!isPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      nextSlide();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, items.length, autoPlayInterval, nextSlide]);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        prevSlide();
      } else if (e.key === 'ArrowRight') {
        nextSlide();
      } else if (e.key === ' ') {
        e.preventDefault();
        togglePlayPause();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [prevSlide, nextSlide]);

  if (!items || items.length === 0) {
    return (
      <div className={`text-center py-12 text-gray-500 ${className}`}>
        <p>{getDisplayText(items[0], 'empty', '暂无内容')}</p>
      </div>
    );
  }

  const currentItem = items[currentIndex];
  const currentTheme = themeClasses[theme];

  return (
    <div className={`relative ${heightClasses[height]} ${className}`}>
      {/* 主要内容区域 */}
      <div className={`
        relative w-full h-full overflow-hidden rounded-lg shadow-lg
        ${layout === 'horizontal' ? 'flex flex-row' : 'flex flex-col'}
      `}>
        {/* 图片区域 */}
        <div className={`
          ${layout === 'horizontal'
            ? `${imagePosition === 'left' ? 'order-1' : 'order-2'} flex-shrink-0 w-2/5`
            : 'w-full h-1/2'
          }
          h-full relative overflow-hidden
          ${imageClassName}
        `}>
          <img
            src={currentItem.image}
            alt={currentItem.imageAlt || currentItem.title}
            className={`
              w-full h-full object-cover transition-all duration-${animationDuration}
              ${animation === 'fade' ? 'opacity-100' : ''}
            `}
          />
        </div>

        {/* 文本区域 */}
        <div className={`
          ${layout === 'horizontal'
            ? `${imagePosition === 'left' ? 'order-2' : 'order-1'} flex-1`
            : 'w-full h-1/2'
          }
          flex flex-col justify-center p-4 sm:p-6 md:p-8 lg:p-12
          bg-white
          ${textClassName}
        `}>
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-800 mb-2 sm:mb-4">
            {currentItem.title}
          </h2>

          {currentItem.description && (
            <p className="text-gray-600 text-sm sm:text-base md:text-lg mb-4 sm:mb-6 leading-relaxed line-clamp-3 md:line-clamp-none">
              {currentItem.description}
            </p>
          )}

          {currentItem.buttonText && (
            <div>
              {currentItem.buttonHref ? (
                <a
                  href={currentItem.buttonHref}
                  className={`
                    inline-flex items-center px-6 py-3 rounded-lg font-medium
                    transition-colors duration-200
                    ${currentTheme.button}
                  `}
                >
                  {currentItem.buttonText}
                </a>
              ) : (
                <Button
                  onClick={currentItem.buttonOnClick}
                  className={currentTheme.button}
                  size="lg"
                >
                  {currentItem.buttonText}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 导航箭头 */}
      {showArrows && items.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="sm"
            onClick={prevSlide}
            disabled={!infinite && currentIndex === 0}
            className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white hover:bg-opacity-75 rounded-full p-1 sm:p-2 z-10"
          >
            <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={nextSlide}
            disabled={!infinite && currentIndex === items.length - 1}
            className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white hover:bg-opacity-75 rounded-full p-1 sm:p-2 z-10"
          >
            <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
          </Button>
        </>
      )}

      {/* 播放/暂停按钮 */}
      {showPlayPause && items.length > 1 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlayPause}
          className="absolute top-4 right-4 bg-black bg-opacity-50 text-white hover:bg-opacity-75 rounded-full p-2"
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
      )}

      {/* 指示器 */}
      {showIndicators && items.length > 1 && (
        <div className="absolute bottom-2 sm:bottom-4 left-1/2 transform -translate-x-1/2 flex gap-1 sm:gap-2 z-10">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`
                w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-200
                ${index === currentIndex
                  ? currentTheme.indicator
                  : currentTheme.indicatorInactive
                }
              `}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Carousel;
