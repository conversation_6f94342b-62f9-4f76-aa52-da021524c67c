import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export interface BreadcrumbItem {
  /** 显示文本 */
  label: string;
  /** 链接地址，如果为空则不可点击 */
  href?: string;
  /** 是否为当前页面（高亮显示） */
  active?: boolean;
  /** 国际化键名 */
  i18nKey?: string;
}

export interface BreadcrumbProps {
  /** 面包屑项目列表 */
  items: BreadcrumbItem[];
  /** 分隔符，默认为 '>' */
  separator?: React.ReactNode;
  /** 是否显示首页图标 */
  showHomeIcon?: boolean;
  /** 前缀文本，默认为 '当前位置：' */
  prefix?: string;
  /** 自定义CSS类名 */
  className?: string;
  /** 文本大小 */
  size?: 'xs' | 'sm' | 'base' | 'lg';
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 点击事件处理 */
  onItemClick?: (item: BreadcrumbItem, index: number) => void;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />,
  showHomeIcon = false,
  prefix = '当前位置：',
  className = '',
  size = 'sm',
  enableI18n = false,
  i18nPrefix = 'breadcrumb',
  onItemClick
}) => {
  const { t } = useTranslation();

  // 尺寸映射
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-xs sm:text-sm',
    base: 'text-sm sm:text-base',
    lg: 'text-base sm:text-lg'
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (item: BreadcrumbItem) => {
    if (enableI18n && item.i18nKey && i18nPrefix) {
      return t(`${i18nPrefix}.${item.i18nKey}`, item.label);
    }
    return item.label;
  };

  // 获取前缀文本
  const getPrefixText = () => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.prefix`, prefix);
    }
    return prefix;
  };

  // 处理项目点击
  const handleItemClick = (item: BreadcrumbItem, index: number) => {
    if (onItemClick) {
      onItemClick(item, index);
    }
  };

  return (
    <nav 
      className={`mb-6 sm:mb-8 ${sizeClasses[size]} text-gray-500 ${className}`}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center flex-wrap gap-1 sm:gap-2">
        {/* 前缀文本 */}
        {prefix && (
          <li className="flex items-center">
            <span>{getPrefixText()}</span>
          </li>
        )}

        {/* 面包屑项目 */}
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {/* 分隔符（除了第一个项目） */}
            {(index > 0 || prefix) && (
              <span className="mx-1 sm:mx-2 text-gray-400">
                {typeof separator === 'string' ? separator : separator}
              </span>
            )}

            {/* 项目内容 */}
            <div className="flex items-center">
              {/* 首页图标（仅第一个项目且启用时显示） */}
              {showHomeIcon && index === 0 && (
                <Home className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
              )}

              {/* 链接或文本 */}
              {item.href && !item.active ? (
                <Link
                  to={item.href}
                  onClick={() => handleItemClick(item, index)}
                  className="text-gray-700 hover:text-red-600 transition-colors duration-200 truncate max-w-[120px] sm:max-w-[200px]"
                  title={getDisplayText(item)}
                >
                  {getDisplayText(item)}
                </Link>
              ) : (
                <span
                  className={`
                    truncate max-w-[120px] sm:max-w-[200px]
                    ${item.active 
                      ? 'text-red-600 font-medium' 
                      : 'text-gray-700'
                    }
                  `}
                  title={getDisplayText(item)}
                >
                  {getDisplayText(item)}
                </span>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
