import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Home,
  FileText,
  MapPin,
  Phone,
  Mail,
  Clock,
  MessageSquare,
  Share2
} from 'lucide-react';
import { cn } from '@/lib/utils';

const Footer = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // Navigation handler
  const handleNavigation = (href: string) => {
    navigate(href);
  };

  // Navigation data structure matching Sidebar component
  const navigation = [
    {
      name: t('Sidebar.navigation.groupIntroduction'),
      href: '/hospital',
      icon: Home,
      children: [
        { name: t('Sidebar.navigation.hospitalOverview'), href: '/hospital/overview' },
        { name: t('Sidebar.navigation.hospitalCulture'), href: '/hospital/culture' },
        { name: t('Sidebar.navigation.hospitalHistory'), href: '/hospital/history' },
        { name: t('Sidebar.navigation.socialCooperation'), href: '/hospital/social-cooperation' },
      ]
    },
    {
      name: t('Sidebar.navigation.newsCenter'),
      icon: FileText,
      children: [
        { name: t('Sidebar.navigation.hospitalNews'), href: '/articles' },
        { name: t('Sidebar.navigation.hospitalStyle'), href: '/news/gallery' },
        { name: t('Sidebar.navigation.videoDisplay'), href: '/news/videos' },
      ]
    },
    {
      name: t('Sidebar.navigation.expertDoctors'),
      href: '/doctors',
      icon: Home,
      children: [
        { name: t('Sidebar.navigation.internalMedicine'), href: '/doctors/internal-medicine' },
        { name: t('Sidebar.navigation.surgery'), href: '/doctors/surgery' },
        { name: t('Sidebar.navigation.dermatology'), href: '/doctors/dermatology' },
        { name: t('Sidebar.navigation.gynecology'), href: '/doctors/gynecology' },
        { name: t('Sidebar.navigation.pediatrics'), href: '/doctors/pediatrics' },
        { name: t('Sidebar.navigation.entOphthalmology'), href: '/doctors/ent-ophthalmology' },
      ]
    },
    {
      name: t('Sidebar.navigation.healthManagement'),
      icon: FileText,
      children: [
        { name: t('Sidebar.navigation.healthPackages'), href: '/health/packages' },
        { name: t('Sidebar.navigation.healthCare'), href: '/health/care' },
      ]
    },
    {
      name: t('Sidebar.navigation.cooperationCases'),
      href: '/cooperation',
      icon: MapPin,
      children: [
        { name: t('Sidebar.navigation.domesticCooperation'), href: '/cooperation/domestic' },
        { name: t('Sidebar.navigation.internationalCooperation'), href: '/cooperation/international' },
      ]
    },
    {
      name: t('Sidebar.navigation.contactUs'),
      href: '/contact',
      icon: MapPin,
      children: [
        { name: t('Sidebar.navigation.contactInfo'), href: '/contact' },
        { name: t('Sidebar.navigation.onlineMessage'), href: '/email' },
      ]
    },
  ];

  return (
    <footer className="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-12">
          {/* Quick Navigation Section */}
          <div className="space-y-4 sm:space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('Footer.quickNavigation')}
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
              {navigation.map((section) => (
                <div key={section.name} className="space-y-2 sm:space-y-3">
                  <h4 className="text-xs sm:text-sm font-medium text-red-600 dark:text-red-400 truncate" title={section.name}>
                    {section.name}
                  </h4>
                  <ul className="space-y-1 sm:space-y-2">
                    {section.children?.slice(0, 3).map((item) => (
                      <li key={item.name}>
                        <button
                          onClick={() => handleNavigation(item.href)}
                          title={item.name}
                          className={cn(
                            "text-xs sm:text-sm transition-colors truncate block text-left w-full hover:underline",
                            location.pathname === item.href
                              ? "text-red-600 dark:text-red-400 font-medium"
                              : "text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400"
                          )}
                        >
                          {item.name}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Online Contact Section */}
          <div className="space-y-4 sm:space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('Footer.onlineContact')}
            </h3>
            <div className="flex flex-row sm:flex-col md:flex-row gap-4 sm:gap-6 justify-center md:justify-start">
              {/* WeChat QR Code Placeholder */}
              <div className="flex flex-col items-center space-y-2 sm:space-y-3">
                <h4 className="text-xs sm:text-sm font-medium text-red-600 dark:text-red-400 flex items-center">
                  <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                  {t('Footer.wechat')}
                </h4>
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 sm:w-7 sm:h-7 text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  扫码关注微信
                </p>
              </div>

              {/* Social Media QR Code Placeholder */}
              <div className="flex flex-col items-center space-y-2 sm:space-y-3">
                <h4 className="text-xs sm:text-sm font-medium text-red-600 dark:text-red-400 flex items-center">
                  <Share2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                  {t('Footer.socialMedia')}
                </h4>
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <Share2 className="w-6 h-6 sm:w-7 sm:h-7 text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  关注我们
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information Section - Full Width */}
        <div className="mt-6 sm:mt-8 lg:mt-10 pt-6 sm:pt-8 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6">
            {t('Footer.contactInfo')}
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {/* Address */}
            <div className="space-y-1 sm:space-y-2">
              <div className="flex items-center text-red-600 dark:text-red-400">
                <MapPin className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                <span className="text-xs sm:text-sm font-medium">{t('Footer.address')}</span>
              </div>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 pl-4 sm:pl-6 leading-relaxed">
                {t('Footer.addressValue')}
              </p>
            </div>

            {/* Phone */}
            <div className="space-y-1 sm:space-y-2">
              <div className="flex items-center text-red-600 dark:text-red-400">
                <Phone className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                <span className="text-xs sm:text-sm font-medium">{t('Footer.phone')}</span>
              </div>
              <div className="pl-4 sm:pl-6 space-y-1">
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                  {t('Footer.phoneValue')}
                </p>
              </div>
            </div>

            {/* Email */}
            <div className="space-y-1 sm:space-y-2">
              <div className="flex items-center text-red-600 dark:text-red-400">
                <Mail className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                <span className="text-xs sm:text-sm font-medium">{t('Footer.email')}</span>
              </div>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 pl-4 sm:pl-6">
                <a
                  href={`mailto:${t('Footer.emailValue')}`}
                  className="hover:text-red-600 dark:hover:text-red-400 transition-colors"
                >
                  {t('Footer.emailValue')}
                </a>
              </p>
            </div>

            {/* Business Hours */}
            <div className="space-y-1 sm:space-y-2">
              <div className="flex items-center text-red-600 dark:text-red-400">
                <Clock className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
                <span className="text-xs sm:text-sm font-medium">{t('Footer.businessHours')}</span>
              </div>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 pl-4 sm:pl-6">
                {t('Footer.businessHoursValue')}
              </p>
            </div>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 text-center sm:text-left">
              {t('Footer.copyright')}
            </p>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleNavigation('/')}
                className="text-xs sm:text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors font-medium"
              >
                {t('common.logo.main')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
