import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

const LanguageSwitcherTop = () => {
  const { i18n } = useTranslation();
  const isMobile = useIsMobile();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  // 根据当前语言和设备类型调整样式
  const isRussian = i18n.language === 'ru';
  const selectClassName = cn(
    "appearance-none bg-white border border-gray-300 rounded-md font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 cursor-pointer",
    isMobile
      ? isRussian
        ? "px-1 py-1 pr-4 text-xs min-w-[100px] max-w-[105px] leading-tight" // 俄语移动端需要更多空间，紧凑行高
        : "px-2 py-1 pr-6 text-xs min-w-[75px]" // 其他语言移动端
      : "px-3 py-1.5 pr-8 text-sm min-w-[110px]" // 桌面端正常大小
  );

  return (
    <div className="relative">
      <select
        onChange={(e) => changeLanguage(e.target.value)}
        value={i18n.language}
        className={selectClassName}
        title="Select Language"
        style={isRussian && isMobile ? {
          fontSize: '11px',
          lineHeight: '1.2',
          paddingTop: '2px',
          paddingBottom: '2px'
        } : undefined} // 俄语移动端使用更小字体和更紧凑的行高
      >
        <option value="zh">中文</option>
        <option value="en">English</option>
        <option value="ru">Русский</option>
      </select>
      {/* Custom dropdown arrow */}
      <div className={cn(
        "absolute inset-y-0 right-0 flex items-center pointer-events-none",
        isMobile
          ? isRussian ? "pr-0.5" : "pr-1" // 俄语状态下箭头更靠右
          : "pr-2"
      )}>
        <svg className={cn(
          "text-gray-400",
          isMobile ? "w-3 h-3" : "w-4 h-4"
        )} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
  );
};

export default LanguageSwitcherTop;
