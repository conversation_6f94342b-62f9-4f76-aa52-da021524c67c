import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

const LanguageSwitcherTop = () => {
  const { i18n } = useTranslation();
  const isMobile = useIsMobile();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  // 移动端使用更紧凑的样式
  const selectClassName = cn(
    "appearance-none bg-white border border-gray-300 rounded-md font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 cursor-pointer",
    isMobile
      ? "px-2 py-1 pr-6 text-xs" // 移动端更紧凑
      : "px-3 py-1.5 pr-8 text-sm" // 桌面端正常大小
  );

  return (
    <div className="relative">
      <select
        onChange={(e) => changeLanguage(e.target.value)}
        value={i18n.language}
        className={selectClassName}
        title="Select Language"
      >
        <option value="zh">中文</option>
        <option value="en">English</option>
        <option value="ru">Русский</option>
      </select>
      {/* Custom dropdown arrow */}
      <div className={cn(
        "absolute inset-y-0 right-0 flex items-center pointer-events-none",
        isMobile ? "pr-1" : "pr-2"
      )}>
        <svg className={cn(
          "text-gray-400",
          isMobile ? "w-3 h-3" : "w-4 h-4"
        )} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
  );
};

export default LanguageSwitcherTop;
