import React, { useRef, useState, useEffect } from 'react';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

export interface HorizontalScrollContainerProps {
  /** 子元素 */
  children: React.ReactNode;
  /** 是否显示箭头控制 */
  showArrows?: boolean;
  /** 箭头主题色彩 */
  arrowTheme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 滚动步长（像素） */
  scrollStep?: number;
  /** 响应式断点配置 */
  responsive?: {
    /** 桌面端：显示网格布局 */
    desktop?: boolean;
    /** 平板端：显示横向滚动 */
    tablet?: boolean;
    /** 移动端：显示横向滚动 */
    mobile?: boolean;
  };
  /** 网格布局配置（桌面端） */
  gridConfig?: {
    cols?: string;
    gap?: string;
  };
  /** 滚动容器配置 */
  scrollConfig?: {
    /** 卡片间距 */
    gap?: string;
    /** 卡片最小宽度 */
    minWidth?: string;
    /** 是否居中对齐 */
    centerAlign?: boolean;
  };
  /** 自定义CSS类名 */
  className?: string;
  /** 容器ID */
  id?: string;
}

const HorizontalScrollContainer: React.FC<HorizontalScrollContainerProps> = ({
  children,
  showArrows = true,
  arrowTheme = 'red',
  scrollStep = 300,
  responsive = {
    desktop: true,
    tablet: true,
    mobile: true
  },
  gridConfig = {
    cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6',
    gap: 'gap-4'
  },
  scrollConfig = {
    gap: 'gap-4',
    minWidth: 'min-w-[280px]',
    centerAlign: true
  },
  className = '',
  id
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // 主题色彩映射
  const themeColors = {
    red: {
      bg: 'bg-red-600 hover:bg-red-700',
      text: 'text-white',
      shadow: 'shadow-red-200'
    },
    blue: {
      bg: 'bg-blue-600 hover:bg-blue-700',
      text: 'text-white',
      shadow: 'shadow-blue-200'
    },
    green: {
      bg: 'bg-green-600 hover:bg-green-700',
      text: 'text-white',
      shadow: 'shadow-green-200'
    },
    purple: {
      bg: 'bg-purple-600 hover:bg-purple-700',
      text: 'text-white',
      shadow: 'shadow-purple-200'
    },
    gray: {
      bg: 'bg-gray-600 hover:bg-gray-700',
      text: 'text-white',
      shadow: 'shadow-gray-200'
    }
  };

  const colors = themeColors[arrowTheme];

  // 检查滚动状态
  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // 滚动函数
  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current && !isScrolling) {
      setIsScrolling(true);
      const scrollAmount = direction === 'left' ? -scrollStep : scrollStep;
      
      scrollContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });

      // 防止快速连续点击
      setTimeout(() => {
        setIsScrolling(false);
        checkScrollButtons();
      }, 300);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      checkScrollButtons();
      container.addEventListener('scroll', checkScrollButtons);
      
      // 监听窗口大小变化
      const handleResize = () => {
        setTimeout(checkScrollButtons, 100);
      };
      window.addEventListener('resize', handleResize);

      return () => {
        container.removeEventListener('scroll', checkScrollButtons);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [children]);

  return (
    <div className={`relative ${className}`} id={id}>
      {/* 桌面端网格布局 */}
      {responsive.desktop && (
        <div className={`hidden xl:grid ${gridConfig.cols} ${gridConfig.gap}`}>
          {children}
        </div>
      )}

      {/* 平板端和移动端横向滚动 */}
      <div className="xl:hidden relative">
        {/* 左箭头 */}
        {showArrows && canScrollLeft && (
          <button
            onClick={() => scroll('left')}
            className={`absolute left-0 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 ${colors.bg} ${colors.text} rounded-full shadow-lg ${colors.shadow} flex items-center justify-center transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500`}
            aria-label="向左滚动"
            disabled={isScrolling}
          >
            <span className="text-lg font-bold">‹</span>
          </button>
        )}

        {/* 右箭头 */}
        {showArrows && canScrollRight && (
          <button
            onClick={() => scroll('right')}
            className={`absolute right-0 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 ${colors.bg} ${colors.text} rounded-full shadow-lg ${colors.shadow} flex items-center justify-center transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500`}
            aria-label="向右滚动"
            disabled={isScrolling}
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        )}

        {/* 滚动容器 */}
        <div
          ref={scrollContainerRef}
          className={`flex overflow-x-auto scrollbar-hide ${scrollConfig.gap} ${showArrows ? 'px-12' : 'px-4'} ${scrollConfig.centerAlign ? 'justify-center md:justify-start' : 'justify-start'}`}
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
        >
          {React.Children.map(children, (child, index) => (
            <div
              key={index}
              className={`flex-shrink-0 ${scrollConfig.minWidth}`}
            >
              {child}
            </div>
          ))}
        </div>
      </div>

      {/* 隐藏滚动条的CSS */}
      <style>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default HorizontalScrollContainer;
