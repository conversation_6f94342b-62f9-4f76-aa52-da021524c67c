import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export interface CardComponentProps {
  /** 卡片ID */
  id?: string | number;
  /** 图片URL */
  image: string;
  /** 图片alt文本 */
  imageAlt?: string;
  /** 标题文本 */
  title: string;
  /** 副标题（可选） */
  subtitle?: string;
  /** 点击跳转链接 */
  href?: string;
  /** 点击事件处理 */
  onClick?: () => void;
  /** 卡片尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** 图片宽高比 */
  imageAspectRatio?: 'square' | 'video' | 'wide' | 'tall' | 'auto';
  /** 是否显示悬停效果 */
  showHoverEffect?: boolean;
  /** 是否显示图片缩放效果 */
  showImageZoom?: boolean;
  /** 是否显示阴影 */
  showShadow?: boolean;
  /** 边框圆角 */
  borderRadius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 文本对齐方式 */
  textAlign?: 'left' | 'center' | 'right';
  /** 文本颜色主题 */
  textTheme?: 'default' | 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 背景颜色 */
  backgroundColor?: 'white' | 'gray' | 'transparent';
  /** 自定义CSS类名 */
  className?: string;
  /** 图片自定义CSS类名 */
  imageClassName?: string;
  /** 文本区域自定义CSS类名 */
  textClassName?: string;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 国际化键名 */
  i18nKey?: string;
  /** 是否在新窗口打开链接 */
  openInNewTab?: boolean;
  /** 自定义数据 */
  data?: any;
}

const CardComponent: React.FC<CardComponentProps> = ({
  id,
  image,
  imageAlt,
  title,
  subtitle,
  href,
  onClick,
  size = 'md',
  imageAspectRatio = 'video',
  showHoverEffect = true,
  showImageZoom = true,
  showShadow = true,
  borderRadius = 'lg',
  textAlign = 'center',
  textTheme = 'default',
  backgroundColor = 'white',
  className = '',
  imageClassName = '',
  textClassName = '',
  enableI18n = false,
  i18nPrefix = 'card',
  i18nKey,
  openInNewTab = false,
  data
}) => {
  const { t } = useTranslation();

  // 尺寸映射
  const sizeClasses = {
    xs: {
      container: 'w-24 h-32',
      text: 'p-2 text-xs',
      title: 'text-xs font-medium'
    },
    sm: {
      container: 'w-32 h-40',
      text: 'p-3 text-sm',
      title: 'text-sm font-medium'
    },
    md: {
      container: 'w-48 h-64',
      text: 'p-4 text-base',
      title: 'text-base font-semibold'
    },
    lg: {
      container: 'w-64 h-80',
      text: 'p-5 text-lg',
      title: 'text-lg font-semibold'
    },
    xl: {
      container: 'w-80 h-96',
      text: 'p-6 text-xl',
      title: 'text-xl font-bold'
    }
  };

  // 图片宽高比映射
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[3/2]',
    tall: 'aspect-[3/4]',
    auto: 'h-auto'
  };

  // 边框圆角映射
  const borderRadiusClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full'
  };

  // 文本对齐映射
  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  // 文本主题映射
  const textThemeClasses = {
    default: 'text-gray-800',
    red: 'text-red-600',
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    gray: 'text-gray-600'
  };

  // 背景颜色映射
  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    transparent: 'bg-transparent'
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (text: string, key?: string) => {
    if (enableI18n && i18nPrefix && (i18nKey || key)) {
      const translationKey = i18nKey || key;
      return t(`${i18nPrefix}.${translationKey}`, text);
    }
    return text;
  };

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // 构建卡片内容
  const cardContent = (
    <div 
      className={`
        ${sizeClasses[size].container}
        ${borderRadiusClasses[borderRadius]}
        ${backgroundClasses[backgroundColor]}
        ${showShadow ? 'shadow-md' : ''}
        ${showHoverEffect ? 'hover:shadow-lg' : ''}
        overflow-hidden transition-all duration-300 group cursor-pointer
        ${className}
      `}
      onClick={handleClick}
    >
      {/* 图片区域 */}
      <div className={`${aspectRatioClasses[imageAspectRatio]} overflow-hidden`}>
        <img
          src={image}
          alt={imageAlt || title}
          className={`
            w-full h-full object-cover
            ${showImageZoom ? 'group-hover:scale-105' : ''}
            transition-transform duration-300
            ${imageClassName}
          `}
          loading="lazy"
        />
      </div>

      {/* 文本区域 */}
      <div className={`
        ${sizeClasses[size].text}
        ${textAlignClasses[textAlign]}
        ${textClassName}
      `}>
        {/* 主标题 */}
        <h3 className={`
          ${sizeClasses[size].title}
          ${textThemeClasses[textTheme]}
          line-clamp-2 leading-tight
          ${showHoverEffect ? 'group-hover:text-opacity-80' : ''}
          transition-colors duration-200
        `}>
          {getDisplayText(title, 'title')}
        </h3>

        {/* 副标题 */}
        {subtitle && (
          <p className={`
            text-gray-500 text-xs mt-1 line-clamp-1
            ${showHoverEffect ? 'group-hover:text-opacity-80' : ''}
            transition-colors duration-200
          `}>
            {getDisplayText(subtitle, 'subtitle')}
          </p>
        )}
      </div>
    </div>
  );

  // 如果有链接，包装在Link组件中
  if (href) {
    // 检查是否是外部链接
    const isExternalLink = href.startsWith('http://') || href.startsWith('https://');

    if (isExternalLink) {
      return (
        <a
          href={href}
          target={openInNewTab ? '_blank' : '_self'}
          rel={openInNewTab ? 'noopener noreferrer' : undefined}
          className="inline-block"
        >
          {cardContent}
        </a>
      );
    } else {
      return (
        <Link
          to={href}
          target={openInNewTab ? '_blank' : undefined}
          rel={openInNewTab ? 'noopener noreferrer' : undefined}
          className="inline-block"
        >
          {cardContent}
        </Link>
      );
    }
  }

  // 否则直接返回卡片内容
  return cardContent;
};

export default CardComponent;