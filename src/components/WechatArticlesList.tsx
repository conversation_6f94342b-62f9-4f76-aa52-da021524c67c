import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Eye, ArrowRight, ExternalLink, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';

export interface WechatArticle {
  /** 文章ID */
  id: string;
  /** 文章标题 */
  title: string;
  /** 文章链接 */
  link: string;
  /** 文章摘要 */
  summary?: string;
  /** 发布日期 */
  publishDate: string;
  /** 文章来源 */
  source: string;
  /** 文章分类 */
  category: 'news' | 'health' | 'education' | 'hospital';
  /** 爬取时间 */
  crawlTime: string;
  /** 公众号名称 */
  account: string;
  /** 文章图片（可选） */
  image?: string;
  /** 阅读量（可选） */
  views?: number;
}

export interface WechatArticlesListProps {
  /** 文章列表 */
  articles: WechatArticle[];
  /** 是否显示图片 */
  showImage?: boolean;
  /** 是否显示分类标签 */
  showCategory?: boolean;
  /** 是否显示日期 */
  showDate?: boolean;
  /** 是否显示来源 */
  showSource?: boolean;
  /** 是否显示外部链接图标 */
  showExternalIcon?: boolean;
  /** 图片尺寸 */
  imageSize?: 'sm' | 'md' | 'lg' | 'xl';
  /** 卡片间距 */
  spacing?: 'tight' | 'normal' | 'loose';
  /** 颜色主题 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 自定义CSS类名 */
  className?: string;
  /** 文章点击事件 */
  onArticleClick?: (article: WechatArticle, index: number) => void;
  /** 空状态显示文本 */
  emptyText?: string;
  /** 是否显示悬停效果 */
  showHoverEffect?: boolean;
  /** 最大显示文章数 */
  maxArticles?: number;
}

const WechatArticlesList: React.FC<WechatArticlesListProps> = ({
  articles,
  showImage = false, // 微信文章通常没有图片
  showCategory = true,
  showDate = true,
  showSource = true,
  showExternalIcon = true,
  imageSize = 'md',
  spacing = 'normal',
  theme = 'red',
  className = '',
  onArticleClick,
  emptyText = '暂无微信文章',
  showHoverEffect = true,
  maxArticles
}) => {
  const { t } = useTranslation();

  // 限制文章数量
  const displayArticles = maxArticles ? articles.slice(0, maxArticles) : articles;

  // 图片尺寸映射
  const imageSizeClasses = {
    sm: 'w-full sm:w-48 h-32',
    md: 'w-full sm:w-72 h-48',
    lg: 'w-full sm:w-80 h-56',
    xl: 'w-full sm:w-96 h-64'
  };

  // 间距映射
  const spacingClasses = {
    tight: 'space-y-3 sm:space-y-4',
    normal: 'space-y-4 sm:space-y-6',
    loose: 'space-y-6 sm:space-y-8'
  };

  // 主题颜色映射
  const themeClasses = {
    red: {
      badge: 'bg-red-100 text-red-800 hover:bg-red-200',
      button: 'text-red-600 hover:text-red-700 hover:bg-red-50',
      border: 'border-red-200',
      accent: 'text-red-600'
    },
    blue: {
      badge: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
      button: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50',
      border: 'border-blue-200',
      accent: 'text-blue-600'
    },
    green: {
      badge: 'bg-green-100 text-green-800 hover:bg-green-200',
      button: 'text-green-600 hover:text-green-700 hover:bg-green-50',
      border: 'border-green-200',
      accent: 'text-green-600'
    },
    purple: {
      badge: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
      button: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50',
      border: 'border-purple-200',
      accent: 'text-purple-600'
    },
    gray: {
      badge: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
      button: 'text-gray-600 hover:text-gray-700 hover:bg-gray-50',
      border: 'border-gray-200',
      accent: 'text-gray-600'
    }
  };

  const currentTheme = themeClasses[theme];

  // 分类标签映射
  const categoryLabels = {
    news: '医院新闻',
    health: '健康科普',
    education: '中医教育',
    hospital: '医院介绍'
  };

  // 处理文章点击
  const handleArticleClick = (article: WechatArticle, index: number) => {
    if (onArticleClick) {
      onArticleClick(article, index);
    }
  };

  // 判断是否为外部链接
  const isExternalLink = (link: string) => {
    return link.startsWith('http') && !link.includes('#');
  };

  // 空状态
  if (displayArticles.length === 0) {
    return (
      <div className="text-center py-12">
        <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500 text-lg">{emptyText}</p>
      </div>
    );
  }

  return (
    <div className={`${spacingClasses[spacing]} ${className}`}>
      {displayArticles.map((article, index) => {
        const isExternal = isExternalLink(article.link);
        
        return (
          <article 
            key={article.id} 
            className={`
              bg-white rounded-lg shadow-md border overflow-hidden 
              ${showHoverEffect ? 'hover:shadow-lg' : ''} 
              transition-shadow duration-200
            `}
          >
            <div className="flex flex-col sm:flex-row">
              {/* 图片 */}
              {showImage && article.image && (
                <div className={`${imageSizeClasses[imageSize]} flex-shrink-0`}>
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              )}

              {/* 内容 */}
              <div className="flex-1 p-4 sm:p-6">
                <div className="flex flex-col h-full">
                  {/* 分类标签 */}
                  {showCategory && (
                    <div className="flex items-center gap-2 mb-3">
                      <Badge 
                        variant="secondary" 
                        className={`${currentTheme.badge} text-xs`}
                      >
                        {categoryLabels[article.category]}
                      </Badge>
                      {showSource && (
                        <Badge 
                          variant="outline" 
                          className="text-xs"
                        >
                          微信公众号
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  {/* 标题 */}
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight">
                    {article.title}
                  </h3>
                  
                  {/* 摘要 */}
                  {article.summary && (
                    <p className="text-gray-600 text-sm sm:text-base mb-4 line-clamp-3 flex-1">
                      {article.summary}
                    </p>
                  )}
                  
                  {/* 底部信息 */}
                  <div className="flex items-center justify-between mt-auto">
                    {/* 日期和来源信息 */}
                    <div className="flex items-center text-gray-500 text-xs sm:text-sm gap-4">
                      {showDate && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span>{article.publishDate}</span>
                        </div>
                      )}
                      {article.views !== undefined && (
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span>{article.views}</span>
                        </div>
                      )}
                    </div>

                    {/* 阅读按钮 */}
                    {isExternal ? (
                      <a
                        href={article.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={() => handleArticleClick(article, index)}
                        className="inline-flex"
                      >
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`${currentTheme.button} text-xs sm:text-sm`}
                        >
                          <span>阅读原文</span>
                          {showExternalIcon && (
                            <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                          )}
                        </Button>
                      </a>
                    ) : (
                      <Link
                        to={article.link}
                        onClick={() => handleArticleClick(article, index)}
                      >
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`${currentTheme.button} text-xs sm:text-sm`}
                        >
                          <span>查看详情</span>
                          <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </article>
        );
      })}
    </div>
  );
};

export default WechatArticlesList;
