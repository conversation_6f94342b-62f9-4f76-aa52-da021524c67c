import React from 'react';
import { useTranslation } from 'react-i18next';

export interface CoreValue {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  icon?: string;
  iconType?: string;
  color?: string;
}

export interface CoreValuesSectionProps {
  /** 标题 */
  title: string;
  /** 副标题 */
  subtitle?: string;
  /** 布局类型 */
  layout?: 'default' | 'enterprise';
  /** 核心价值观数据 */
  values: CoreValue[];
  /** 主题色彩 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 自定义CSS类名 */
  className?: string;
  /** 是否启用国际化 */
  enableI18n?: boolean;
}

const CoreValuesSection: React.FC<CoreValuesSectionProps> = ({
  title,
  subtitle,
  layout = 'default',
  values,
  theme = 'red',
  className = '',
  enableI18n = false
}) => {
  const { t } = useTranslation();

  // 图标映射
  const iconMap: { [key: string]: string } = {
    lightbulb: '💡',
    award: '🏆',
    heart: '❤️',
    star: '⭐',
    medical: '🏥',
    innovation: '🔬',
    excellence: '🎯',
    heritage: '📚'
  };

  // 获取图标
  const getIcon = (value: CoreValue) => {
    if (value.icon) return value.icon;
    if (value.iconType && iconMap[value.iconType]) return iconMap[value.iconType];
    return '💡'; // 默认图标
  };

  // 主题色彩映射
  const themeColors = {
    red: {
      primary: 'text-red-600',
      secondary: 'text-red-500',
      bg: 'bg-red-50',
      border: 'border-red-200',
      accent: 'bg-red-600'
    },
    blue: {
      primary: 'text-blue-600',
      secondary: 'text-blue-500',
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      accent: 'bg-blue-600'
    },
    green: {
      primary: 'text-green-600',
      secondary: 'text-green-500',
      bg: 'bg-green-50',
      border: 'border-green-200',
      accent: 'bg-green-600'
    },
    purple: {
      primary: 'text-purple-600',
      secondary: 'text-purple-500',
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      accent: 'bg-purple-600'
    },
    gray: {
      primary: 'text-gray-600',
      secondary: 'text-gray-500',
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      accent: 'bg-gray-600'
    }
  };

  const colors = themeColors[theme];

  if (layout === 'enterprise') {
    return (
      <section className={`bg-gray-50 py-16 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 标题区域 */}
          {title && (
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {enableI18n ? t(title) : title}
              </h2>
              {subtitle && (
                <p className="text-lg text-gray-600">
                  {enableI18n ? t(subtitle) : subtitle}
                </p>
              )}
            </div>
          )}

          {/* 价值观网格 */}
          <div className="relative grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
            {values.map((value, index) => (
              <div key={value.id} className="text-center relative">
                {/* 图标容器 */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-red-100">
                    <span className="text-3xl">{getIcon(value)}</span>
                  </div>
                  {/* 装饰性圆圈 */}
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-600 rounded-full opacity-20"></div>
                </div>

                {/* 内容区域 */}
                <div className="space-y-3">
                  <h3 className="text-xl font-bold text-gray-900">
                    {enableI18n ? t(value.title) : value.title}
                  </h3>

                  {value.subtitle && (
                    <p className="text-sm font-medium text-red-600 uppercase tracking-wide">
                      {enableI18n ? t(value.subtitle) : value.subtitle}
                    </p>
                  )}

                  <p className="text-gray-600 leading-relaxed text-sm">
                    {enableI18n ? t(value.description) : value.description}
                  </p>
                </div>

                {/* 连接线（除了最后一个） */}
                {index < values.length - 1 && (
                  <div className="hidden md:block absolute top-10 left-full transform -translate-x-1/2 w-8 lg:w-12 h-px bg-red-200 z-0"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // 默认布局
  return (
    <section className={`bg-white rounded-lg shadow-lg p-8 ${className}`}>
      {/* 标题区域 */}
      {title && (
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {enableI18n ? t(title) : title}
          </h2>
          {subtitle && (
            <p className="text-gray-600">
              {enableI18n ? t(subtitle) : subtitle}
            </p>
          )}
        </div>
      )}

      {/* 价值观网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {values.map((value) => (
          <div
            key={value.id}
            className={`text-center p-6 ${colors.bg} rounded-lg border ${colors.border} hover:shadow-md transition-shadow duration-200`}
          >
            {/* 图标 */}
            <div className="text-3xl mb-4">
              {getIcon(value)}
            </div>
            
            {/* 标题 */}
            <div className={`text-xl font-bold ${colors.primary} mb-3`}>
              {enableI18n ? t(value.title) : value.title}
            </div>
            
            {/* 副标题 */}
            {value.subtitle && (
              <div className={`text-sm font-medium ${colors.secondary} mb-2`}>
                {enableI18n ? t(value.subtitle) : value.subtitle}
              </div>
            )}
            
            {/* 描述 */}
            <p className="text-gray-600 text-sm leading-relaxed">
              {enableI18n ? t(value.description) : value.description}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default CoreValuesSection;
