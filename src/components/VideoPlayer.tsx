import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

export interface VideoSource {
  /** 视频URL */
  src: string;
  /** 视频类型 */
  type?: string;
  /** 视频质量标签 */
  quality?: string;
}

export interface VideoPlayerProps {
  /** 视频源列表 */
  sources: VideoSource[] | string;
  /** 视频标题 */
  title?: string;
  /** 封面图片 */
  poster?: string;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 是否循环播放 */
  loop?: boolean;
  /** 是否静音 */
  muted?: boolean;
  /** 是否显示控制栏 */
  controls?: boolean;
  /** 是否显示自定义控制栏 */
  customControls?: boolean;
  /** 宽高比 */
  aspectRatio?: 'video' | 'square' | 'wide' | 'cinema';
  /** 播放器尺寸 */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 是否响应式 */
  responsive?: boolean;
  /** 自定义CSS类名 */
  className?: string;
  /** 播放事件回调 */
  onPlay?: () => void;
  /** 暂停事件回调 */
  onPause?: () => void;
  /** 结束事件回调 */
  onEnded?: () => void;
  /** 错误事件回调 */
  onError?: (error: string) => void;
  /** 时间更新回调 */
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 错误提示文本 */
  errorText?: string;
  /** 加载提示文本 */
  loadingText?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  sources,
  title,
  poster,
  autoPlay = false,
  loop = false,
  muted = false,
  controls = true,
  customControls = false,
  aspectRatio = 'video',
  size = 'md',
  responsive = true,
  className = '',
  onPlay,
  onPause,
  onEnded,
  onError,
  onTimeUpdate,
  enableI18n = false,
  i18nPrefix = 'video',
  errorText = '视频加载失败',
  loadingText = '加载中...'
}) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // 宽高比映射
  const aspectRatioClasses = {
    video: 'aspect-video',
    square: 'aspect-square',
    wide: 'aspect-[21/9]',
    cinema: 'aspect-[2.35/1]'
  };

  // 尺寸映射
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-2xl',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    full: 'w-full'
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (key: string, defaultText: string) => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.${key}`, defaultText);
    }
    return defaultText;
  };

  // 处理视频源
  const getVideoSources = (): VideoSource[] => {
    if (typeof sources === 'string') {
      return [{ src: sources }];
    }
    return sources;
  };

  // 播放/暂停切换
  const togglePlay = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
  };

  // 静音切换
  const toggleMute = () => {
    if (!videoRef.current) return;
    
    videoRef.current.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (!videoRef.current) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      videoRef.current.requestFullscreen();
    }
  };

  // 重新播放
  const restart = () => {
    if (!videoRef.current) return;
    
    videoRef.current.currentTime = 0;
    videoRef.current.play();
  };

  // 进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const newTime = (clickX / width) * duration;
    
    videoRef.current.currentTime = newTime;
  };

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 视频事件处理
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      setIsPlaying(true);
      setIsLoading(false);
      if (onPlay) onPlay();
    };

    const handlePause = () => {
      setIsPlaying(false);
      if (onPause) onPause();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      if (onEnded) onEnded();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      if (onTimeUpdate) {
        onTimeUpdate(video.currentTime, video.duration);
      }
    };

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);
    };

    const handleError = () => {
      setHasError(true);
      setIsLoading(false);
      setErrorMessage(getDisplayText('error', errorText));
      if (onError) onError(errorMessage);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      setHasError(false);
    };

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);
    video.addEventListener('loadstart', handleLoadStart);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
      video.removeEventListener('loadstart', handleLoadStart);
    };
  }, [onPlay, onPause, onEnded, onTimeUpdate, onError, errorMessage, errorText, enableI18n, i18nPrefix]);

  return (
    <div className={`
      ${responsive ? 'w-full' : sizeClasses[size]}
      ${responsive ? 'mx-auto' : ''}
      ${className}
    `}>
      {/* 视频标题 */}
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-gray-800">
          {title}
        </h3>
      )}

      {/* 视频容器 */}
      <div className={`
        relative ${aspectRatioClasses[aspectRatio]} 
        bg-black rounded-lg overflow-hidden shadow-lg
      `}>
        {/* 视频元素 */}
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          poster={poster}
          autoPlay={autoPlay}
          loop={loop}
          muted={muted}
          controls={controls && !customControls}
          playsInline
        >
          {getVideoSources().map((source, index) => (
            <source
              key={index}
              src={source.src}
              type={source.type || 'video/mp4'}
            />
          ))}
          {getDisplayText('unsupported', '您的浏览器不支持视频播放')}
        </video>

        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p>{getDisplayText('loading', loadingText)}</p>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
            <div className="text-white text-center">
              <p className="mb-4">{errorMessage}</p>
              <Button 
                onClick={() => window.location.reload()} 
                variant="outline"
                className="text-white border-white hover:bg-white hover:text-black"
              >
                {getDisplayText('retry', '重试')}
              </Button>
            </div>
          </div>
        )}

        {/* 自定义控制栏 */}
        {customControls && !hasError && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
            {/* 进度条 */}
            <div 
              className="w-full h-1 bg-gray-600 rounded-full mb-3 cursor-pointer"
              onClick={handleProgressClick}
            >
              <div 
                className="h-full bg-white rounded-full transition-all duration-200"
                style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>

            {/* 控制按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={togglePlay}
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={toggleMute}
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={restart}
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-white text-sm">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </span>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={toggleFullscreen}
                  className="text-white hover:bg-white hover:bg-opacity-20"
                >
                  <Maximize className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
