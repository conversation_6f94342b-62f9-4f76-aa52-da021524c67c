import React from 'react';

export interface TitleProps {
  /** 主标题 */
  title: string;
  /** 副标题 */
  subtitle?: string;
  /** 尺寸大小 */
  size?: 'sm' | 'md' | 'lg';
  /** 文本对齐方式 */
  align?: 'left' | 'center' | 'right';
  /** 自定义CSS类名 */
  className?: string;
  /** 标题标签类型 */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const Title: React.FC<TitleProps> = ({
  title,
  subtitle,
  size = 'md',
  align = 'left',
  className = '',
  as = 'h2'
}) => {
  // 尺寸映射
  const sizeClasses = {
    sm: {
      title: 'text-lg sm:text-xl',
      subtitle: 'text-sm sm:text-base'
    },
    md: {
      title: 'text-xl sm:text-2xl md:text-3xl',
      subtitle: 'text-base sm:text-lg'
    },
    lg: {
      title: 'text-2xl sm:text-3xl md:text-4xl',
      subtitle: 'text-lg sm:text-xl'
    }
  };

  // 对齐方式映射
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  // 动态创建标题元素
  const TitleElement = as;

  return (
    <div className={`space-y-2 ${alignClasses[align]} ${className}`}>
      {/* 主标题 */}
      <TitleElement className={`
        ${sizeClasses[size].title}
        text-gray-900
        font-bold
        leading-tight
      `}>
        {title}
      </TitleElement>

      {/* 副标题 */}
      {subtitle && (
        <p className={`
          ${sizeClasses[size].subtitle}
          text-red-600
          leading-relaxed
        `}>
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default Title;
