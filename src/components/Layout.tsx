
import React, { useState } from 'react';
import Sidebar from './Sidebar';
import TopNavigation from './TopNavigation';
import Footer from './Footer';
import FloatingContactWidget from './FloatingContactWidget';
import { Menu } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePageTitle } from '@/hooks/usePageTitle';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const isMobile = useIsMobile();

  // 设置默认标题（仅医院名称）
  usePageTitle();

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  const expandSidebar = () => {
    if (!isMobile) {
      setSidebarCollapsed(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col w-full bg-gray-50">
      {/* Show TopNavigation on mobile OR when sidebar is collapsed on desktop */}
      {(isMobile || (!isMobile && sidebarCollapsed)) && (
        <TopNavigation
          onExpandSidebar={!isMobile && sidebarCollapsed ? expandSidebar : undefined}
          showExpandButton={!isMobile && sidebarCollapsed}
        />
      )}

      {/* Desktop collapse button - only show when sidebar is expanded */}
      {!isMobile && !sidebarCollapsed && (
        <button
          onClick={toggleSidebar}
          className="fixed top-4 left-4 z-50 p-2 bg-red-600 text-white rounded-md shadow-lg"
        >
          <Menu className="w-5 h-5" />
        </button>
      )}

      {/* Mobile overlay */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main content area with sidebar */}
      <div className="flex flex-1">
        {/* Sidebar - only show on desktop when not collapsed, or on mobile when open */}
        {(!isMobile && !sidebarCollapsed) || (isMobile && sidebarOpen) ? (
          <div className={`
            ${isMobile ? 'fixed inset-y-0 left-0 z-40 w-80' : 'sticky top-0 w-80 h-screen'}
            transition-transform duration-300 ease-in-out
          `}>
            <Sidebar
              onClose={() => setSidebarOpen(false)}
              collapsed={false}
            />
          </div>
        ) : null}

        {/* Main content */}
        <main className={`
          flex-1 overflow-auto min-h-0
          ${(isMobile || (!isMobile && sidebarCollapsed)) ? 'pt-16' : ''}
          ${!isMobile && !sidebarCollapsed ? 'pl-16' : ''}
        `}>
          <div className="min-h-full flex flex-col">
            <div className="flex-1">
              {children}
            </div>
            <Footer />
          </div>
        </main>
      </div>

      {/* 全局浮动联系窗口 */}
      <FloatingContactWidget />
    </div>
  );
};

export default Layout;
