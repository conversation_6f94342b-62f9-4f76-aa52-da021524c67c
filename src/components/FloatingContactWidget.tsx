import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Phone,
  ChevronUp,
  Mail
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface FloatingContactWidgetProps {
  className?: string;
}

const FloatingContactWidget: React.FC<FloatingContactWidgetProps> = ({ className }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // 监听滚动位置，决定是否显示回到顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 入场动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 1000); // 1秒后显示

    return () => clearTimeout(timer);
  }, []);

  // 平滑滚动到顶部
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // 处理在线留言点击
  const handleOnlineMessageClick = () => {
    navigate('/contact/email');
  };



  return (
    <div className={cn(
      "fixed z-50 flex flex-col items-center space-y-2 transition-all duration-500 ease-in-out",
      isMobile ? "right-2 bottom-2" : "right-4 bottom-4",
      isVisible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
      className
    )}>
      {/* 二维码区域 */}
      <div className="w-24 bg-white shadow-lg rounded-lg border border-gray-200 p-3 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2 mx-auto overflow-hidden">
          {/* 实际项目中可以替换为真实的二维码图片 */}
          <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSI4IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjU2IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjgiIHk9IjU2IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjMyIiB5PSIzMiIgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJibGFjayIvPgo8L3N2Zz4K"
            alt="WeChat QR Code"
            className="w-full h-full object-cover"
          />
        </div>
        <p className="text-xs text-gray-500">{t('FloatingWidget.wechatContact')}</p>
      </div>

      {/* 电话区域 */}
      <div className="w-24 bg-white shadow-lg rounded-lg border border-gray-200 p-3 flex flex-col items-center">
        <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mb-2">
          <Phone className="w-4 h-4 text-red-600" />
        </div>
        <div className="flex justify-center w-full">
          <p className="text-xs text-gray-800 font-medium">18604491518</p>
        </div>
      </div>

      {/* 在线留言区域 */}
      <button
        onClick={handleOnlineMessageClick}
        className="w-24 bg-white shadow-lg rounded-lg border border-gray-200 p-3 hover:bg-blue-50 transition-colors group flex flex-col items-center"
        aria-label={t('FloatingWidget.onlineMessage')}
      >
        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
          <Mail className="w-4 h-4 text-blue-600 group-hover:scale-110 transition-transform" />
        </div>
        <div className="flex justify-center w-full">
          <p className="text-xs text-gray-800 font-medium">{t('FloatingWidget.onlineMessage')}</p>
        </div>
      </button>

      {/* 回到顶部按钮 */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className={cn(
            "w-24 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg p-3",
            "flex flex-col items-center justify-center transition-all duration-300 ease-in-out",
            "hover:scale-110 active:scale-95"
          )}
          aria-label={t('FloatingWidget.backToTop')}
          title={t('FloatingWidget.backToTop')}
        >
          <ChevronUp className="w-4 h-4 mb-1" />
          <p className="text-xs">{t('FloatingWidget.backToTop')}</p>
        </button>
      )}
    </div>
  );
};

export default FloatingContactWidget;
