import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
  MenubarTrigger,
} from '@/components/ui/menubar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Newspaper,
  Stethoscope,
  Heart,
  BookOpen,
  Handshake,
  Phone,
  Award,
  GraduationCap,
  ChevronRight,
  Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileNavigationMenuProps {
  onItemClick?: () => void;
  className?: string;
}

const MobileNavigationMenu: React.FC<MobileNavigationMenuProps> = ({
  onItemClick,
  className
}) => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // 复用Sidebar的导航数据结构
  const navigation = [
    { 
      name: t('Sidebar.navigation.groupIntroduction'), 
      href: '/hospital', 
      icon: Building2,
      children: [
        { name: t('Sidebar.navigation.hospitalOverview'), href: '/hospital/overview' },
        { name: t('Sidebar.navigation.hospitalCulture'), href: '/hospital/culture' },
        { name: t('Sidebar.navigation.hospitalHistory'), href: '/hospital/history' },
        { name: t('Sidebar.navigation.socialCooperation'), href: '/hospital/social-cooperation' },
      ]
    },
    {
      name: t('Sidebar.navigation.newsCenter'),
      icon: Newspaper,
      children: [
        { name: t('Sidebar.navigation.hospitalNews'), href: '/news' },
        { name: t('Sidebar.navigation.hospitalStyle'), href: '/news/gallery' },
        { name: t('Sidebar.navigation.videoDisplay'), href: '/news/videos' },
      ]
    },
    { 
      name: t('Sidebar.navigation.expertDoctors'), 
      href: '/doctors', 
      icon: Stethoscope,
      children: [
        { name: t('Sidebar.navigation.internalMedicine'), href: '/doctors/internal-medicine' },
        { name: t('Sidebar.navigation.surgery'), href: '/doctors/surgery' },
        { name: t('Sidebar.navigation.dermatology'), href: '/doctors/dermatology' },
        { name: t('Sidebar.navigation.gynecology'), href: '/doctors/gynecology' },
        { name: t('Sidebar.navigation.pediatrics'), href: '/doctors/pediatrics' },
        { name: t('Sidebar.navigation.entOphthalmology'), href: '/doctors/ent-ophthalmology' },
        { name: t('Sidebar.navigation.acupunctureMassage'), href: '/doctors/acupuncture-massage' },
        { name: t('Sidebar.navigation.ultrasound'), href: '/doctors/ultrasound' },
        { name: t('Sidebar.navigation.proctology'), href: '/doctors/proctology' },
        { name: t('Sidebar.navigation.tcmRehabilitation'), href: '/doctors/tcm-rehabilitation' },
      ]
    },
    {
      name: t('Sidebar.navigation.healthManagement'),
      icon: Heart,
      children: [
        { name: t('Sidebar.navigation.healthPackages'), href: '/health/packages' },
        { name: t('Sidebar.navigation.healthCare'), href: '/health/care' },
      ]
    },
    {
      name: t('Sidebar.navigation.scienceTherapy'),
      icon: BookOpen,
      children: [
        { name: t('Sidebar.navigation.tcmScience'), href: '/therapy/tcm-science' },
        { name: t('Sidebar.navigation.specialTherapy'), href: '/therapy/special' },
      ]
    },
    { name: t('Sidebar.navigation.exportBase'), href: '/special/export-base', icon: Award },
    { name: t('Sidebar.navigation.educationBase'), href: '/special/education-base', icon: GraduationCap },
    { 
      name: t('Sidebar.navigation.cooperationCases'), 
      href: '/cooperation', 
      icon: Handshake,
      children: [
        { name: t('Sidebar.navigation.domesticCooperation'), href: '/cooperation/domestic' },
        { name: t('Sidebar.navigation.internationalCooperation'), href: '/cooperation/international' },
      ]
    },
    {
      name: t('Sidebar.navigation.contactUs'),
      href: '/contact',
      icon: Phone,
      children: [
        { name: t('Sidebar.navigation.contactInfo'), href: '/contact' },
        { name: t('Sidebar.navigation.onlineMessage'), href: '/contact/email' },
      ]
    },
  ];

  const handleItemClick = (href?: string) => {
    if (href) {
      navigate(href);
    }
    if (onItemClick) {
      onItemClick();
    }
  };



  const isActive = (href: string) => location.pathname === href;

  return (
    <>
      <style>{`
        .mobile-menu-left-align {
          left: 0px !important;
          transform: translateX(0px) !important;
          margin-left: 0px !important;
          right: auto !important;
        }
      `}</style>
      <Menubar className={cn("border-none bg-transparent p-0 h-auto", className)}>
        <MenubarMenu value="mobile-menu">
          <MenubarTrigger className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md data-[state=open]:bg-red-50 data-[state=open]:text-red-600 focus:bg-red-50 focus:text-red-600">
            <Menu className="w-4 h-4 mr-1" />
            <span className="hidden sm:inline">{t('TopNavigation.navigation.menu')}</span>
          </MenubarTrigger>
        <MenubarContent
          className="max-h-96 overflow-y-auto border-gray-200 shadow-lg w-[45vw] min-w-[160px] max-w-[200px] sm:w-[40vw] sm:max-w-[220px] lg:w-64 mobile-menu-left-align"
          align="start"
          side="bottom"
          sideOffset={8}
          alignOffset={-200}
          style={{ zIndex: 9998 }}
        >
          {navigation.map((item, index) => (
            <React.Fragment key={item.name}>
              {item.children ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <MenubarItem
                      className={cn(
                        "flex items-center w-full py-3 px-2 text-sm cursor-pointer",
                        item.href && isActive(item.href) && "bg-red-50 text-red-600",
                        "hover:bg-gray-50 focus:bg-red-50 focus:text-red-600 data-[state=open]:bg-red-50 data-[state=open]:text-red-600"
                      )}
                    >
                      <item.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="flex-1 text-left truncate">{item.name}</span>
                      <ChevronRight className="w-4 h-4 ml-1 flex-shrink-0" />
                    </MenubarItem>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="border-gray-200 shadow-lg w-[50vw] min-w-[180px] max-w-[240px] sm:w-[45vw] sm:max-w-[260px] lg:w-56"
                    side="right"
                    align="start"
                    sideOffset={8}
                  >
                    {item.children.map((child) => (
                      <DropdownMenuItem
                        key={child.name}
                        className={cn(
                          "cursor-pointer text-sm py-2.5 px-2 min-h-[40px] flex items-center",
                          "hover:bg-gray-50 focus:bg-red-50 focus:text-red-600",
                          "whitespace-normal break-words leading-snug",
                          isActive(child.href) && "bg-red-50 text-red-600 font-medium"
                        )}
                        onClick={() => handleItemClick(child.href)}
                      >
                        <span className="w-full text-left">{child.name}</span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <MenubarItem
                  className={cn(
                    "flex items-center cursor-pointer py-3 px-2 text-sm min-h-[44px]",
                    "hover:bg-gray-50 focus:bg-red-50 focus:text-red-600",
                    item.href && isActive(item.href) && "bg-red-50 text-red-600 font-medium"
                  )}
                  onClick={() => handleItemClick(item.href)}
                >
                  <item.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </MenubarItem>
              )}
              {index < navigation.length - 1 && <MenubarSeparator />}
            </React.Fragment>
          ))}
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
    </>
  );
};

export default MobileNavigationMenu;
