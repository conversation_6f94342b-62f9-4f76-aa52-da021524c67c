
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Search, Home, Newspaper, Handshake, Phone, Mail, ChevronDown, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useResponsiveLayout } from '@/hooks/use-responsive-layout';
import LanguageSwitcherTop from './LanguageSwitcherTop';
import MobileNavigationMenu from './MobileNavigationMenu';
import { useTranslation } from 'react-i18next';

interface TopNavigationProps {
  onExpandSidebar?: () => void;
  showExpandButton?: boolean;
}

const TopNavigation = ({ onExpandSidebar, showExpandButton = false }: TopNavigationProps) => {
  const {t} = useTranslation();
  const location = useLocation();
  const [isNavOpen, setIsNavOpen] = useState(false);
  const isMobile = useIsMobile();

  // 使用响应式布局Hook
  const { layoutConfig, dynamicStyles } = useResponsiveLayout();

  const navigation = [
    { name: t('TopNavigation.navigation.home'), href: '/', icon: Home },
    { name: t('TopNavigation.navigation.news'), href: '/articles', icon: Newspaper },
    { name: t('TopNavigation.navigation.cooperation'), href: '/hospital/social-cooperation', icon: Handshake },
    { name: t('TopNavigation.navigation.contact'), href: '/contact', icon: Phone },
    { name: t('TopNavigation.navigation.email'), href: '/email', icon: Mail },
  ];
 
  return (
    <div className="fixed top-0 left-0 right-0 z-30 bg-white border-b border-gray-200 shadow-sm">
      <div
        className="flex items-center justify-between py-2 max-w-full"
        style={dynamicStyles.containerStyle}
      >
        {/* Left section - Mobile menu button always first, then logo */}
        <div className={cn(
          "flex items-center flex-shrink-0",
          isMobile ? "space-x-1" : "space-x-2"
        )}>
          {/* Mobile Navigation Menu - always positioned first on mobile */}
          {isMobile && (
            <div className="flex-shrink-0">
              <MobileNavigationMenu
                onItemClick={() => setIsNavOpen(false)}
                className="relative"
                showText={layoutConfig.menuButtonText}
              />
            </div>
          )}

          {/* Expand button for desktop when sidebar is collapsed */}
          {!isMobile && showExpandButton && onExpandSidebar && (
            <button
              onClick={onExpandSidebar}
              className="p-2 text-red-600 hover:bg-red-50 rounded-md"
            >
              <Menu className="w-5 h-5" />
            </button>
          )}

          {/* Logo */}
          <div
            className={cn(
              "text-red-600 font-bold flex-shrink min-w-0",
              isMobile ? "text-base" : "text-lg"
            )}
            style={dynamicStyles.logoStyle}
            title={t('common.logo.main')}
          >
            <span className="truncate">
              {t('common.logo.main')}
            </span>
          </div>
        </div>

        {/* Desktop Navigation - only show if not too cramped */}
        <nav className="hidden lg:flex items-center space-x-1 flex-1 justify-center max-w-md">
          {navigation.slice(0, 3).map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                location.pathname === item.href
                  ? "bg-red-50 text-red-600"
                  : "text-gray-700 hover:bg-gray-100"
              )}
            >
              <item.icon className="w-4 h-4 mr-1" />
              <span className="hidden xl:inline">{item.name}</span>
            </Link>
          ))}
          
          {/* More navigation dropdown for remaining items */}
          {navigation.length > 3 && (
            <div className="relative">
              <button
                onClick={() => setIsNavOpen(!isNavOpen)}
                className="flex items-center px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
              >
                <span className="hidden xl:inline">{t('TopNavigation.navigation.more')}</span>
                <ChevronDown className="w-3 h-3 ml-1" />
              </button>
              
              {isNavOpen && (
                <>
                  <div 
                    className="fixed inset-0 z-10" 
                    onClick={() => setIsNavOpen(false)}
                  />
                  <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-20">
                    <div className="py-1">
                      {navigation.slice(3).map((item) => (
                        <Link
                          key={item.name}
                          to={item.href}
                          onClick={() => setIsNavOpen(false)}
                          title={item.name}
                          className={cn(
                            "flex items-center px-4 py-2 text-sm transition-colors",
                            location.pathname === item.href
                              ? "bg-red-50 text-red-600"
                              : "text-gray-700 hover:bg-gray-100"
                          )}
                        >
                          <item.icon className="w-4 h-4 mr-3 flex-shrink-0" />
                          <span className="truncate">{item.name}</span>
                        </Link>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </nav>



        {/* Right section */}
        <div className={cn(
          "flex items-center flex-shrink-0",
          isMobile ? "space-x-1" : "space-x-2"
        )}>
          {/* Language Dropdown - 优先显示，根据语言调整样式 */}
          <div className="relative flex-shrink-0 order-last">
             <LanguageSwitcherTop />
          </div>

          {/* Desktop Search Input */}
          {layoutConfig.searchVisible && (
            <div className="relative hidden md:block flex-shrink">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder={t("TopNavigation.placeholder")}
                className="w-32 lg:w-48 pl-10 pr-4 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Mobile search button - 根据布局配置显示 */}
          {layoutConfig.searchVisible && (
            <button className="md:hidden p-2 text-gray-600 hover:bg-gray-100 rounded-md flex-shrink-0">
              <Search className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopNavigation;
