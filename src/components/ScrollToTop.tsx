import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop组件
 * 监听路由变化，自动滚动到页面顶部
 * 优化：确保滚动的是正确的容器，避免与Sidebar滚动冲突
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // 延迟执行，确保页面内容已渲染，避免与Sidebar滚动冲突
    const timer = setTimeout(() => {
      // 首先尝试滚动主内容区域（Layout中的main元素）
      const mainContent = document.getElementById('main-content');
      if (mainContent) {
        mainContent.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      } else {
        // 如果没有找到主内容区域，则滚动整个窗口
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }, 300); // 延迟执行，确保Sidebar滚动先完成

    return () => clearTimeout(timer);
  }, [pathname]);

  return null;
};

export default ScrollToTop;
