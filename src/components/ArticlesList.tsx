import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Eye, ArrowRight, LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Article } from '@/hooks/useArticles';

export interface ArticlesListProps {
  /** 文章列表 */
  articles: Article[];
  /** 是否显示图片 */
  showImage?: boolean;
  /** 是否显示分类标签 */
  showCategory?: boolean;
  /** 是否显示日期 */
  showDate?: boolean;
  /** 是否显示浏览量 */
  showViews?: boolean;
  /** 是否显示作者 */
  showAuthor?: boolean;
  /** 是否显示阅读更多按钮 */
  showReadMore?: boolean;
  /** 图片尺寸 */
  imageSize?: 'sm' | 'md' | 'lg' | 'xl';
  /** 卡片间距 */
  spacing?: 'tight' | 'normal' | 'loose';
  /** 颜色主题 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 自定义CSS类名 */
  className?: string;
  /** 文章点击事件 */
  onArticleClick?: (article: Article, index: number) => void;
  /** 阅读更多按钮文本 */
  readMoreText?: string;
  /** 阅读更多按钮图标 */
  readMoreIcon?: LucideIcon;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 空状态显示文本 */
  emptyText?: string;
  /** 是否显示悬停效果 */
  showHoverEffect?: boolean;
}

const ArticlesList: React.FC<ArticlesListProps> = ({
  articles,
  showImage = true,
  showCategory = true,
  showDate = true,
  showViews = true,
  showAuthor = false,
  showReadMore = true,
  imageSize = 'md',
  spacing = 'normal',
  theme = 'red',
  className = '',
  onArticleClick,
  readMoreText = '阅读更多',
  readMoreIcon = ArrowRight,
  enableI18n = false,
  i18nPrefix = 'articles',
  emptyText = '暂无文章',
  showHoverEffect = true
}) => {
  const { t } = useTranslation();

  // 图片尺寸映射
  const imageSizeClasses = {
    sm: 'w-full sm:w-48 h-32',
    md: 'w-full sm:w-72 h-48',
    lg: 'w-full sm:w-80 h-56',
    xl: 'w-full sm:w-96 h-64'
  };

  // 间距映射
  const spacingClasses = {
    tight: 'space-y-3 sm:space-y-4',
    normal: 'space-y-4 sm:space-y-6',
    loose: 'space-y-6 sm:space-y-8'
  };

  // 主题颜色映射
  const themeClasses = {
    red: {
      category: 'bg-red-100 text-red-600',
      button: 'text-red-600 hover:text-red-700 hover:bg-red-50',
      titleHover: 'hover:text-red-600'
    },
    blue: {
      category: 'bg-blue-100 text-blue-600',
      button: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50',
      titleHover: 'hover:text-blue-600'
    },
    green: {
      category: 'bg-green-100 text-green-600',
      button: 'text-green-600 hover:text-green-700 hover:bg-green-50',
      titleHover: 'hover:text-green-600'
    },
    purple: {
      category: 'bg-purple-100 text-purple-600',
      button: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50',
      titleHover: 'hover:text-purple-600'
    },
    gray: {
      category: 'bg-gray-100 text-gray-600',
      button: 'text-gray-600 hover:text-gray-700 hover:bg-gray-50',
      titleHover: 'hover:text-gray-600'
    }
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (key: string, defaultText: string) => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.${key}`, defaultText);
    }
    return defaultText;
  };

  // 处理文章点击
  const handleArticleClick = (article: Article, index: number) => {
    if (onArticleClick) {
      onArticleClick(article, index);
    }
  };

  // 获取文章链接
  const getArticleHref = (article: Article) => {
    return article.href || `/article/${article.id}`;
  };

  const ReadMoreIcon = readMoreIcon;
  const currentTheme = themeClasses[theme];

  // 空状态
  if (!articles || articles.length === 0) {
    return (
      <div className={`text-center py-12 text-gray-500 ${className}`}>
        <p>{getDisplayText('empty', emptyText)}</p>
      </div>
    );
  }

  return (
    <div className={`${spacingClasses[spacing]} ${className}`}>
      {articles.map((article, index) => (
        <article 
          key={article.id} 
          className={`
            bg-white rounded-lg shadow-md border overflow-hidden 
            ${showHoverEffect ? 'hover:shadow-lg' : ''} 
            transition-shadow duration-200
          `}
        >
          <div className="flex flex-col sm:flex-row">
            {/* 图片 */}
            {showImage && article.image && (
              <div className={`${imageSizeClasses[imageSize]} flex-shrink-0`}>
                <img
                  src={article.image}
                  alt={article.title}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            )}
            
            {/* 内容 */}
            <div className="flex-1 p-4 sm:p-6 flex flex-col justify-between">
              <div>
                {/* 元信息行 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3">
                  {/* 分类标签 */}
                  {showCategory && article.category && (
                    <span className={`
                      px-3 py-1 text-xs font-medium rounded-full w-fit
                      ${currentTheme.category}
                    `}>
                      {article.category}
                    </span>
                  )}
                  
                  {/* 日期和浏览量 */}
                  <div className="flex items-center text-gray-500 text-xs sm:text-sm gap-4">
                    {showDate && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span>{article.date}</span>
                      </div>
                    )}
                    {showViews && article.views !== undefined && (
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span>{article.views}</span>
                      </div>
                    )}
                    {showAuthor && article.author && (
                      <span>{article.author}</span>
                    )}
                  </div>
                </div>
                
                {/* 标题 */}
                <h2 className={`
                  text-lg sm:text-xl font-bold text-gray-800 mb-2 sm:mb-3 
                  line-clamp-2 transition-colors duration-200
                  ${showHoverEffect ? currentTheme.titleHover : ''}
                `}>
                  <Link 
                    to={getArticleHref(article)}
                    onClick={() => handleArticleClick(article, index)}
                  >
                    {article.title}
                  </Link>
                </h2>
                
                {/* 摘要 */}
                {article.excerpt && (
                  <p className="text-sm sm:text-base text-gray-600 line-clamp-3 leading-relaxed">
                    {article.excerpt}
                  </p>
                )}
              </div>
              
              {/* 阅读更多按钮 */}
              {showReadMore && (
                <div className="flex justify-end mt-3 sm:mt-4">
                  <Link to={getArticleHref(article)}>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className={`${currentTheme.button} text-sm`}
                    >
                      {getDisplayText('readMore', readMoreText)}
                      <ReadMoreIcon className="ml-1 w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </article>
      ))}
    </div>
  );
};

export default ArticlesList;
