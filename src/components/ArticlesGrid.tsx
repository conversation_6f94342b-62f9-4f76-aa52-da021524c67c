import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Eye, ArrowRight, LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Article } from '@/hooks/useArticles';

export interface ArticlesGridProps {
  /** 文章列表 */
  articles: Article[];
  /** 网格列数配置 */
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  /** 是否显示图片 */
  showImage?: boolean;
  /** 是否显示分类标签 */
  showCategory?: boolean;
  /** 是否显示日期 */
  showDate?: boolean;
  /** 是否显示浏览量 */
  showViews?: boolean;
  /** 是否显示作者 */
  showAuthor?: boolean;
  /** 是否显示阅读更多按钮 */
  showReadMore?: boolean;
  /** 图片宽高比 */
  imageAspectRatio?: 'square' | 'video' | 'wide' | 'tall';
  /** 卡片间距 */
  spacing?: 'tight' | 'normal' | 'loose';
  /** 颜色主题 */
  theme?: 'red' | 'blue' | 'green' | 'purple' | 'gray';
  /** 自定义CSS类名 */
  className?: string;
  /** 文章点击事件 */
  onArticleClick?: (article: Article, index: number) => void;
  /** 阅读更多按钮文本 */
  readMoreText?: string;
  /** 阅读更多按钮图标 */
  readMoreIcon?: LucideIcon;
  /** 是否启用国际化 */
  enableI18n?: boolean;
  /** 国际化键前缀 */
  i18nPrefix?: string;
  /** 空状态显示文本 */
  emptyText?: string;
  /** 是否显示悬停效果 */
  showHoverEffect?: boolean;
  /** 是否显示图片缩放效果 */
  showImageZoom?: boolean;
}

const ArticlesGrid: React.FC<ArticlesGridProps> = ({
  articles,
  columns = { sm: 1, md: 2, lg: 3, xl: 3 },
  showImage = true,
  showCategory = true,
  showDate = true,
  showViews = true,
  showAuthor = false,
  showReadMore = true,
  imageAspectRatio = 'video',
  spacing = 'normal',
  theme = 'red',
  className = '',
  onArticleClick,
  readMoreText = '阅读更多',
  readMoreIcon = ArrowRight,
  enableI18n = false,
  i18nPrefix = 'articles',
  emptyText = '暂无文章',
  showHoverEffect = true,
  showImageZoom = true
}) => {
  const { t } = useTranslation();

  // 网格列数映射
  const getGridClasses = () => {
    const { sm = 1, md = 2, lg = 3, xl = 3 } = columns;
    return `grid grid-cols-${sm} md:grid-cols-${md} lg:grid-cols-${lg} xl:grid-cols-${xl}`;
  };

  // 图片宽高比映射
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[3/2]',
    tall: 'aspect-[3/4]'
  };

  // 间距映射
  const spacingClasses = {
    tight: 'gap-4',
    normal: 'gap-6 sm:gap-8',
    loose: 'gap-8 sm:gap-12'
  };

  // 主题颜色映射
  const themeClasses = {
    red: {
      category: 'bg-red-100 text-red-600',
      button: 'text-red-600 hover:text-red-700 hover:bg-red-50',
      titleHover: 'group-hover:text-red-600'
    },
    blue: {
      category: 'bg-blue-100 text-blue-600',
      button: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50',
      titleHover: 'group-hover:text-blue-600'
    },
    green: {
      category: 'bg-green-100 text-green-600',
      button: 'text-green-600 hover:text-green-700 hover:bg-green-50',
      titleHover: 'group-hover:text-green-600'
    },
    purple: {
      category: 'bg-purple-100 text-purple-600',
      button: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50',
      titleHover: 'group-hover:text-purple-600'
    },
    gray: {
      category: 'bg-gray-100 text-gray-600',
      button: 'text-gray-600 hover:text-gray-700 hover:bg-gray-50',
      titleHover: 'group-hover:text-gray-600'
    }
  };

  // 获取显示文本（支持国际化）
  const getDisplayText = (key: string, defaultText: string) => {
    if (enableI18n && i18nPrefix) {
      return t(`${i18nPrefix}.${key}`, defaultText);
    }
    return defaultText;
  };

  // 处理文章点击
  const handleArticleClick = (article: Article, index: number) => {
    if (onArticleClick) {
      onArticleClick(article, index);
    }
  };

  // 获取文章链接
  const getArticleHref = (article: Article) => {
    return article.href || `/article/${article.id}`;
  };

  const ReadMoreIcon = readMoreIcon;
  const currentTheme = themeClasses[theme];

  // 空状态
  if (!articles || articles.length === 0) {
    return (
      <div className={`text-center py-12 text-gray-500 ${className}`}>
        <p>{getDisplayText('empty', emptyText)}</p>
      </div>
    );
  }

  return (
    <div className={`${getGridClasses()} ${spacingClasses[spacing]} ${className}`}>
      {articles.map((article, index) => (
        <Link
          key={article.id}
          to={getArticleHref(article)}
          onClick={() => handleArticleClick(article, index)}
          className="block"
        >
          <article
            className={`
              bg-white rounded-lg shadow-md border overflow-hidden group cursor-pointer
              ${showHoverEffect ? 'hover:shadow-lg' : ''}
              transition-shadow duration-300
            `}
          >
          {/* 图片 */}
          {showImage && article.image && (
            <div className={`${aspectRatioClasses[imageAspectRatio]} overflow-hidden`}>
              <img
                src={article.image}
                alt={article.title}
                className={`
                  w-full h-full object-cover
                  ${showImageZoom ? 'group-hover:scale-105' : ''}
                  transition-transform duration-300
                `}
                loading="lazy"
              />
            </div>
          )}
          
          {/* 内容 */}
          <div className="p-4 sm:p-6">
            {/* 元信息行 */}
            <div className="flex items-center justify-between mb-3">
              {/* 分类标签 */}
              {showCategory && article.category && (
                <span className={`
                  px-3 py-1 text-xs font-medium rounded-full
                  ${currentTheme.category}
                `}>
                  {article.category}
                </span>
              )}
              
              {/* 浏览量 */}
              {showViews && article.views !== undefined && (
                <div className="flex items-center text-gray-500 text-sm gap-4">
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{article.views}</span>
                  </div>
                </div>
              )}
            </div>
            
            {/* 标题 */}
            <h3 className={`
              text-lg font-bold text-gray-800 mb-3 line-clamp-2
              transition-colors duration-200
              ${showHoverEffect ? currentTheme.titleHover : ''}
            `}>
              {article.title}
            </h3>
            
            {/* 摘要 */}
            {article.excerpt && (
              <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed mb-4">
                {article.excerpt}
              </p>
            )}
            
            {/* 底部信息 */}
            <div className="flex items-center justify-between">
              {/* 日期和作者 */}
              <div className="flex items-center text-gray-500 text-sm gap-2">
                {showDate && (
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{article.date}</span>
                  </div>
                )}
                {showAuthor && article.author && (
                  <span>• {article.author}</span>
                )}
              </div>
              
              {/* 阅读更多按钮 */}
              {showReadMore && (
                <Button
                  variant="ghost"
                  size="sm"
                  className={`${currentTheme.button} p-2 pointer-events-none`}
                >
                  <span className="text-xs">{getDisplayText('readMore', readMoreText)}</span>
                  <ReadMoreIcon className="w-4 h-4 ml-1" />
                </Button>
              )}
            </div>
          </div>
        </article>
        </Link>
      ))}
    </div>
  );
};

export default ArticlesGrid;
