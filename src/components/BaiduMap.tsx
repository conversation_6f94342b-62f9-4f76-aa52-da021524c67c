import React, { useEffect, useRef, useState, useCallback } from 'react';
import { MapPin, Loader2, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';

interface BaiduMapProps {
  address: string;
  hospitalName: string;
  className?: string;
  height?: string;
}

declare global {
  interface Window {
    BMap: any;
    BMapGL: any;
    BMAP_STATUS_SUCCESS: any;
  }
}

const BaiduMap: React.FC<BaiduMapProps> = ({
  address,
  hospitalName,
  className = '',
  height = 'h-64 sm:h-80 md:h-96'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mapInstance, setMapInstance] = useState<any>(null);
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const isMobile = useIsMobile();
  const { t } = useTranslation();

  // 百度地图导航功能
  const handleBaiduNavigation = useCallback(() => {
    if (!isMobile) return;

    // 构建百度地图URL Scheme
    const baiduMapUrl = `baidumap://map/direction?destination=${encodeURIComponent(address)}&mode=driving&src=大连东海医院官网`;

    try {
      // 尝试打开百度地图APP
      window.location.href = baiduMapUrl;

      // 如果APP未安装，2秒后跳转到网页版
      setTimeout(() => {
        const webUrl = `https://map.baidu.com/search/${encodeURIComponent(address)}`;
        window.open(webUrl, '_blank');
      }, 2000);
    } catch (error) {
      console.error('百度地图导航失败:', error);
      // 直接打开网页版百度地图
      const webUrl = `https://map.baidu.com/search/${encodeURIComponent(address)}`;
      window.open(webUrl, '_blank');
    }
  }, [isMobile, address]);

  // 高德地图导航功能
  const handleGaodeNavigation = useCallback(() => {
    if (!isMobile) return;

    // 检测设备类型并构建相应的URL Scheme
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const lat = coordinates?.lat || '';
    const lng = coordinates?.lng || '';

    let gaodeMapUrl: string;
    if (isIOS) {
      // iOS URL Scheme
      gaodeMapUrl = `iosamap://navi?sourceApplication=大连东海医院官网&poiname=${encodeURIComponent(hospitalName)}&lat=${lat}&lon=${lng}&dev=0&style=2`;
    } else {
      // Android URL Scheme
      gaodeMapUrl = `androidamap://navi?sourceApplication=大连东海医院官网&poiname=${encodeURIComponent(hospitalName)}&lat=${lat}&lon=${lng}&dev=0&style=2`;
    }

    try {
      // 尝试打开高德地图APP
      window.location.href = gaodeMapUrl;

      // 如果APP未安装，3秒后跳转到网页版
      setTimeout(() => {
        const webUrl = `https://uri.amap.com/navigation?to=${lng},${lat},${encodeURIComponent(address)}&src=大连东海医院官网&callnative=1`;
        window.open(webUrl, '_blank');
      }, 3000);
    } catch (error) {
      console.error('高德地图导航失败:', error);
      // 直接打开网页版高德地图
      const webUrl = `https://uri.amap.com/navigation?to=${lng},${lat},${encodeURIComponent(address)}&src=大连东海医院官网&callnative=1`;
      window.open(webUrl, '_blank');
    }
  }, [isMobile, address, hospitalName, coordinates]);

  useEffect(() => {
    // 检查百度地图API是否已加载
    if (typeof window.BMap !== 'undefined') {
      initializeMap();
    } else {
      loadBaiduMapScript();
    }

    // 添加消息监听器处理导航请求
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'navigate-baidu') {
        handleBaiduNavigation();
      } else if (event.data.type === 'navigate-gaode') {
        handleGaodeNavigation();
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      // 清理地图实例
      if (mapInstance) {
        mapInstance.destroy();
      }
      // 清理消息监听器
      window.removeEventListener('message', handleMessage);
    };
  }, [handleBaiduNavigation, handleGaodeNavigation]);

  const loadBaiduMapScript = () => {
    // 检查脚本是否已存在
    if (document.querySelector('script[src*="api.map.baidu.com"]')) {
      return;
    }

    // 获取API密钥
    const apiKey = import.meta.env.VITE_BAIDU_MAP_AK || 'demo_api_key_for_development';

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://api.map.baidu.com/api?v=3.0&ak=${apiKey}&callback=initBaiduMap`;
    script.async = true;
    script.onerror = () => {
      setError('百度地图API加载失败，请检查网络连接或API密钥');
      setLoading(false);
    };

    // 设置全局回调函数
    (window as any).initBaiduMap = () => {
      initializeMap();
    };

    document.head.appendChild(script);
  };



  const initializeMap = () => {
    if (!mapRef.current || typeof window.BMap === 'undefined') {
      setError('地图容器或API未准备就绪');
      setLoading(false);
      return;
    }

    try {
      // 创建地图实例
      const map = new window.BMap.Map(mapRef.current);
      setMapInstance(map);

      // 创建地理编码器实例
      const geoCoder = new window.BMap.Geocoder();

      // 对地址进行解析
      geoCoder.getPoint(address, (point: any) => {
        if (point) {
          // 保存坐标信息
          setCoordinates({ lat: point.lat, lng: point.lng });

          // 初始化地图，设置中心点坐标和地图级别
          map.centerAndZoom(point, 16);

          // 启用滚轮缩放
          map.enableScrollWheelZoom(true);

          // 添加地图控件
          map.addControl(new window.BMap.NavigationControl());
          map.addControl(new window.BMap.ScaleControl());
          map.addControl(new window.BMap.OverviewMapControl());
          map.addControl(new window.BMap.MapTypeControl());

          // 创建自定义图标
          const icon = new window.BMap.Icon(
            'data:image/svg+xml;base64,' + btoa(`
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#dc2626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                <circle cx="12" cy="10" r="3"/>
              </svg>
            `),
            new window.BMap.Size(32, 32),
            {
              anchor: new window.BMap.Size(16, 32)
            }
          );

          // 创建标注
          const marker = new window.BMap.Marker(point, { icon });
          map.addOverlay(marker);

          // 创建信息窗口内容
          const isMobileDevice = window.innerWidth < 768;

          // 获取翻译文本
          const goThereText = t('BaiduMap.goThere');
          const baiduNavigationText = t('BaiduMap.baiduNavigation');
          const gaodeNavigationText = t('BaiduMap.gaodeNavigation');

          const infoWindowContent = `
            <div style="padding: ${isMobileDevice ? '2px' : '12px'}; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: ${isMobileDevice ? '200px' : '300px'}; overflow: hidden;">
              ${isMobileDevice ? `
                <div style="margin-bottom: 2px;">
                  <span style="color: #374151; font-size: 9px; line-height: 1.1; display: block; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${address}</span>
                </div>
                <div style="position: relative;">
                  <div class="navigation-dropdown" style="position: relative; display: inline-block; width: 100%;">
                    <button
                      class="dropdown-trigger"
                      onclick="toggleDropdown(this)"
                      style="
                        background: #dc2626;
                        color: white;
                        border: none;
                        padding: 3px 6px;
                        border-radius: 2px;
                        font-size: 9px;
                        font-weight: 500;
                        cursor: pointer;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 2px;
                        height: 20px;
                        box-sizing: border-box;
                        margin: 0;
                      "
                      onmouseover="this.style.background='#b91c1c'"
                      onmouseout="this.style.background='#dc2626'"
                    >
                      <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
                      </svg>
                      ${goThereText}
                      <svg width="6" height="6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 2px;">
                        <polyline points="6,9 12,15 18,9"></polyline>
                      </svg>
                    </button>
                    <div
                      class="dropdown-menu"
                      style="
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: white;
                        border: 1px solid #e5e7eb;
                        border-radius: 2px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                        z-index: 1000;
                        display: none;
                        margin-top: 1px;
                      "
                    >
                      <div
                        onclick="window.parent.postMessage({type: 'navigate-baidu'}, '*'); hideDropdown()"
                        style="
                          padding: 4px 6px;
                          font-size: 9px;
                          color: #374151;
                          cursor: pointer;
                          border-bottom: 1px solid #f3f4f6;
                        "
                        onmouseover="this.style.background='#f9fafb'"
                        onmouseout="this.style.background='white'"
                      >
                        ${baiduNavigationText}
                      </div>
                      <div
                        onclick="window.parent.postMessage({type: 'navigate-gaode'}, '*'); hideDropdown()"
                        style="
                          padding: 4px 6px;
                          font-size: 9px;
                          color: #374151;
                          cursor: pointer;
                        "
                        onmouseover="this.style.background='#f9fafb'"
                        onmouseout="this.style.background='white'"
                      >
                        ${gaodeNavigationText}
                      </div>
                    </div>
                  </div>
                </div>
                <script>
                  function toggleDropdown(button) {
                    const dropdown = button.parentNode;
                    const menu = dropdown.querySelector('.dropdown-menu');
                    const isVisible = menu.style.display === 'block';

                    // 隐藏所有其他下拉菜单
                    document.querySelectorAll('.dropdown-menu').forEach(m => m.style.display = 'none');

                    // 切换当前下拉菜单
                    menu.style.display = isVisible ? 'none' : 'block';
                  }

                  function hideDropdown() {
                    document.querySelectorAll('.dropdown-menu').forEach(m => m.style.display = 'none');
                  }

                  // 点击外部区域关闭下拉菜单
                  document.addEventListener('click', function(e) {
                    if (!e.target.closest('.navigation-dropdown')) {
                      hideDropdown();
                    }
                  });
                </script>
              ` : `
                <div style="margin-bottom: 8px;">
                  <span style="color: #6b7280; font-size: 14px; font-weight: 500;">地址：</span>
                  <span style="color: #374151; font-size: 14px; line-height: 1.2;">${address}</span>
                </div>
                <div style="margin-bottom: 8px;">
                  <span style="color: #6b7280; font-size: 14px; font-weight: 500;">电话：</span>
                  <span style="color: #374151; font-size: 14px;">0411-39656855</span>
                </div>
              `}
            </div>
          `;

          const infoWindow = new window.BMap.InfoWindow(
            infoWindowContent,
            {
              width: isMobileDevice ? 200 : 320,
              height: isMobileDevice ? 50 : 140,
              title: hospitalName
            }
          );

          // 标注点击事件
          marker.addEventListener('click', () => {
            map.openInfoWindow(infoWindow, point);
          });

          // 延迟打开信息窗口，确保地图完全加载
          setTimeout(() => {
            map.openInfoWindow(infoWindow, point);
          }, 500);

          setLoading(false);
        } else {
          setError('地址解析失败，请检查地址是否正确');
          setLoading(false);
        }
      }, hospitalName);

    } catch (err) {
      console.error('地图初始化失败:', err);
      setError('地图初始化失败');
      setLoading(false);
    }
  };

  if (error) {
    return (
      <div className={`${height} ${className} bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center`}>
        <div className="text-center p-6 max-w-md">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-amber-500" />
          <p className="text-lg font-medium mb-2 text-gray-800">地图暂时无法加载</p>
          <p className="text-sm text-gray-600 mb-6">{error}</p>

          {/* 备用地址信息卡片 */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-start gap-4 mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <MapPin className="w-5 h-5 text-red-600" />
              </div>
              <div className="text-left flex-1">
                <h4 className="font-bold text-gray-800 text-lg mb-2">{hospitalName}</h4>
                <p className="text-gray-600 mb-2">{address}</p>
                <p className="text-gray-600 text-sm">电话：0411-39656855</p>
              </div>
            </div>

            {/* 导航提示 */}
            <div className="border-t border-gray-100 pt-4">
              <p className="text-sm text-gray-500 mb-3">您可以使用以下方式查看位置：</p>
              <div className="flex flex-wrap gap-2">
                <a
                  href={`https://map.baidu.com/search/${encodeURIComponent(address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200 transition-colors"
                >
                  百度地图
                </a>
                <a
                  href={`https://ditu.amap.com/search?query=${encodeURIComponent(address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-md text-sm hover:bg-green-200 transition-colors"
                >
                  高德地图
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${height} ${className} relative rounded-lg overflow-hidden`}>
      {loading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center text-gray-500">
            <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
            <p className="text-sm">正在加载地图...</p>
          </div>
        </div>
      )}
      <div 
        ref={mapRef} 
        className="w-full h-full"
        style={{ minHeight: '200px' }}
      />
    </div>
  );
};

export default BaiduMap;
