# 微信公众号文章集成系统

本文档介绍如何在医院网站中集成微信公众号文章的完整解决方案。

## 📋 目录

- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [使用指南](#使用指南)
- [自动化部署](#自动化部署)
- [故障排除](#故障排除)
- [API 文档](#api-文档)

## 🎯 系统概述

### 功能特性

- ✅ **自动爬取**: 定期爬取微信公众号文章
- ✅ **分类管理**: 自动分类文章（医院新闻、健康科普、中医教育等）
- ✅ **响应式设计**: 支持桌面和移动端显示
- ✅ **实时更新**: 支持手动和自动刷新
- ✅ **外链跳转**: 直接跳转到微信文章原文
- ✅ **搜索筛选**: 支持关键词搜索和分类筛选
- ✅ **缓存机制**: 本地JSON缓存，提高加载速度
- ✅ **错误处理**: 完善的错误处理和降级方案

### 目标公众号

- **公众号名称**: 大连协和中西医结合医院
- **微信号**: dl-xiehe
- **账号主体**: 大连东海医院

## 🏗️ 技术架构

### 前端组件

```
src/
├── components/
│   ├── WechatArticlesList.tsx    # 微信文章列表组件
│   └── ...
├── hooks/
│   ├── useWechatArticles.ts      # 微信文章数据管理Hook
│   └── ...
├── pages/
│   └── wechat/
│       └── WechatArticles.tsx    # 微信文章页面
└── data/
    └── wechat/                   # 微信文章数据目录
        ├── wechat-all.json       # 全部文章
        ├── wechat-news.json      # 医院新闻
        ├── wechat-health.json    # 健康科普
        ├── wechat-education.json # 中医教育
        └── wechat-hospital.json  # 医院介绍
```

### 后端脚本

```
scripts/
├── wechat-crawler.js           # 微信文章爬虫
├── update-wechat-articles.sh   # 自动更新脚本
├── test-rss-sources.js         # RSS源测试脚本
└── crontab-example.txt         # 定时任务配置示例
```

### 自动化流程

```
GitHub Actions → 定时触发 → 运行爬虫 → 生成JSON → 提交更新 → 部署网站
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装项目依赖
pnpm install

# 安装爬虫依赖
cd scripts
npm install cheerio node-fetch
```

### 2. 手动运行爬虫

```bash
cd scripts
node wechat-crawler.js
```

### 3. 查看生成的数据

```bash
ls -la ../src/data/wechat/
cat ../src/data/wechat/wechat-all.json
```

### 4. 启动开发服务器

```bash
cd ..
pnpm dev
```

### 5. 访问微信文章页面

打开浏览器访问: `http://localhost:5173/wechat/articles`

## ⚙️ 配置说明

### 爬虫配置

编辑 `scripts/wechat-crawler.js` 中的配置:

```javascript
const CONFIG = {
  // 目标公众号信息
  wechatAccount: {
    name: '大连协和中西医结合医院',
    wechatId: 'dl-xiehe',
    entity: '大连东海医院'
  },
  
  // 输出配置
  outputDir: '../src/data/wechat',
  maxArticles: 50,
  
  // 请求配置
  requestDelay: 2000,
  timeout: 15000
};
```

### 自动更新配置

编辑 `scripts/update-wechat-articles.sh` 中的路径:

```bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DATA_DIR="$PROJECT_ROOT/src/data/wechat"
```

### GitHub Actions 配置

编辑 `.github/workflows/update-wechat-articles.yml`:

```yaml
# 定时执行 - 每天早上8点和下午6点
schedule:
  - cron: '0 0,10 * * *'  # UTC时间
```

## 📖 使用指南

### 在页面中使用微信文章组件

```tsx
import React from 'react';
import WechatArticlesList from '@/components/WechatArticlesList';
import { useWechatArticles } from '@/hooks/useWechatArticles';

const MyPage = () => {
  const { articles, loading, error } = useWechatArticles({
    category: 'health',
    pageSize: 10
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <WechatArticlesList
      articles={articles}
      theme="red"
      showCategory={true}
      showExternalIcon={true}
    />
  );
};
```

### 分类筛选

```tsx
import { WECHAT_CATEGORIES } from '@/hooks/useWechatArticles';

// 可用分类
const categories = [
  { id: 'all', label: '全部文章' },
  { id: 'news', label: '医院新闻' },
  { id: 'health', label: '健康科普' },
  { id: 'education', label: '中医教育' },
  { id: 'hospital', label: '医院介绍' }
];
```

### 手动刷新数据

```tsx
const { refresh } = useWechatArticles();

const handleRefresh = async () => {
  await refresh();
};
```

## 🤖 自动化部署

### GitHub Actions 自动化

系统已配置 GitHub Actions 工作流，会自动:

1. **定时执行**: 每天早上8点和下午6点
2. **爬取文章**: 运行微信文章爬虫
3. **生成数据**: 创建分类JSON文件
4. **提交更新**: 自动提交到Git仓库
5. **部署网站**: 触发网站重新部署

### 服务器定时任务

对于自托管服务器，可以使用 crontab:

```bash
# 编辑 crontab
crontab -e

# 添加定时任务
0 8 * * * cd /path/to/project/scripts && ./update-wechat-articles.sh
0 18 * * * cd /path/to/project/scripts && ./update-wechat-articles.sh
```

### 手动更新

```bash
# 运行完整更新流程
cd scripts
./update-wechat-articles.sh

# 仅运行爬虫
node wechat-crawler.js

# 查看更新日志
tail -f wechat-update.log
```

## 🔧 故障排除

### 常见问题

#### 1. 爬虫无法获取文章

**原因**: RSS源不可用或网络问题
**解决**: 
- 检查网络连接
- 运行RSS源测试: `node test-rss-sources.js`
- 使用手动添加的文章数据作为备用

#### 2. JSON文件格式错误

**原因**: 数据生成过程中出错
**解决**:
- 检查爬虫日志
- 验证JSON格式: `node -e "JSON.parse(require('fs').readFileSync('path/to/file.json'))"`
- 恢复备份数据

#### 3. 组件无法加载数据

**原因**: 文件路径错误或权限问题
**解决**:
- 检查文件是否存在: `ls -la src/data/wechat/`
- 验证文件权限
- 检查网络请求路径

#### 4. 自动更新失败

**原因**: 脚本权限或环境问题
**解决**:
- 检查脚本权限: `chmod +x update-wechat-articles.sh`
- 验证依赖安装: `node --version`, `pnpm --version`
- 查看详细日志

### 调试命令

```bash
# 测试爬虫
cd scripts
node wechat-crawler.js

# 测试RSS源
node test-rss-sources.js

# 验证JSON格式
node -e "console.log(JSON.parse(require('fs').readFileSync('../src/data/wechat/wechat-all.json', 'utf8')))"

# 查看更新日志
tail -f scripts/wechat-update.log

# 检查文件权限
ls -la scripts/update-wechat-articles.sh
```

## 📚 API 文档

### WechatArticlesList 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| articles | WechatArticle[] | - | 文章列表 |
| showCategory | boolean | true | 显示分类标签 |
| showDate | boolean | true | 显示发布日期 |
| showExternalIcon | boolean | true | 显示外链图标 |
| theme | string | 'red' | 主题颜色 |
| maxArticles | number | - | 最大显示数量 |

### useWechatArticles Hook

```tsx
const {
  articles,        // 文章列表
  total,          // 总数量
  loading,        // 加载状态
  error,          // 错误信息
  lastUpdate,     // 最后更新时间
  accountInfo,    // 公众号信息
  refresh,        // 刷新函数
  filterArticles, // 筛选函数
  getCategoryStats // 分类统计
} = useWechatArticles(options);
```

### 数据格式

```typescript
interface WechatArticle {
  id: string;
  title: string;
  link: string;
  summary?: string;
  publishDate: string;
  source: string;
  category: 'news' | 'health' | 'education' | 'hospital';
  crawlTime: string;
  account: string;
}
```

## 📝 更新日志

### v1.0.0 (2025-07-15)

- ✅ 初始版本发布
- ✅ 微信文章爬虫实现
- ✅ React组件和Hook开发
- ✅ GitHub Actions自动化
- ✅ 完整文档编写

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
