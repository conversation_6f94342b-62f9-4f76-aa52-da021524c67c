# 百度地图API集成配置指南

## 概述

本项目已集成百度地图API，用于在ContactUs页面显示医院位置信息。本文档将指导您如何配置和使用百度地图功能。

## 功能特性

- ✅ 动态地图显示医院位置
- ✅ 自定义标记点和信息窗口
- ✅ 地图缩放、拖拽等交互功能
- ✅ 响应式设计，支持移动端和桌面端
- ✅ 加载状态和错误处理
- ✅ 备用静态地址信息显示

## 配置步骤

### 1. 申请百度地图API密钥

1. 访问 [百度地图开放平台](https://lbsyun.baidu.com/)
2. 注册并登录账号
3. 进入控制台，创建新应用
4. 选择应用类型为"浏览器端"
5. 填写应用名称和域名信息
6. 获取API密钥（AK）

### 2. 配置环境变量

1. 复制 `.env.example` 文件为 `.env`：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，替换API密钥：
   ```env
   VITE_BAIDU_MAP_AK=your_actual_api_key_here
   ```

### 3. 重启开发服务器

```bash
npm run dev
# 或
pnpm dev
```

## 使用说明

### 基本用法

```tsx
import BaiduMap from '@/components/BaiduMap';

<BaiduMap
  address="辽宁省大连市旅顺口区龙海路168-9"
  hospitalName="大连东海医院"
  className="shadow-lg border border-gray-200"
  height="h-64 sm:h-80 md:h-96"
/>
```

### 组件属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| address | string | ✅ | - | 医院地址 |
| hospitalName | string | ✅ | - | 医院名称 |
| className | string | ❌ | '' | 自定义CSS类名 |
| height | string | ❌ | 'h-64 sm:h-80 md:h-96' | 地图容器高度 |

## 错误处理

当百度地图API无法加载时，组件会自动显示备用的静态地址信息，包括：

- 医院名称和地址
- 联系电话
- 外部地图链接（百度地图、高德地图）

## 技术实现

### 核心功能

1. **动态脚本加载**：根据环境变量动态加载百度地图API
2. **地理编码**：将地址转换为经纬度坐标
3. **地图初始化**：设置地图中心点、缩放级别和控件
4. **标记点**：在医院位置添加自定义标记
5. **信息窗口**：显示医院详细信息

### 地图配置

- **缩放级别**：16（适合显示周边环境）
- **地图控件**：导航控件、比例尺、鹰眼图、地图类型切换
- **交互功能**：滚轮缩放、拖拽移动

## 故障排除

### 常见问题

1. **地图无法加载**
   - 检查API密钥是否正确配置
   - 确认域名是否在百度地图控制台中正确设置
   - 检查网络连接是否正常

2. **地址解析失败**
   - 确认地址格式是否正确
   - 尝试使用更详细的地址信息

3. **环境变量不生效**
   - 确认 `.env` 文件位于项目根目录
   - 重启开发服务器
   - 检查变量名是否以 `VITE_` 开头

### 调试模式

在开发环境中，可以通过浏览器控制台查看详细的错误信息和调试日志。

## 生产环境部署

1. 确保生产环境的域名已在百度地图控制台中配置
2. 设置生产环境的环境变量
3. 测试地图功能是否正常工作

## 相关文件

- `src/components/BaiduMap.tsx` - 百度地图组件
- `src/pages/contact/ContactUs.tsx` - 联系我们页面
- `.env.example` - 环境变量模板
- `.env` - 环境变量配置文件

## 更新日志

- **v1.0.0** - 初始版本，支持基本地图显示和标记功能
- 支持响应式设计和错误处理
- 集成备用静态地址信息显示
