# 微信公众号文章系统部署指南

## 🎯 最终确认的可行方案

经过实际测试，以下是**确实可行**的部署方案：

### ✅ 方案一：微信公众号API（推荐）
- **前提**：需要公众号管理员权限
- **优点**：官方接口、数据准确、自动化程度高
- **实施**：配置AppID和AppSecret后自动获取

### ✅ 方案二：手动导入（立即可用）
- **前提**：无需任何权限
- **优点**：100%可控、立即可用
- **实施**：使用提供的导入工具

### ❌ 不可行方案
- **RSS源**：所有测试的RSS源都不可用
- **网页爬虫**：搜狗微信搜索无法获取到目标公众号

## 🚀 立即部署步骤

### 第一步：验证当前状态

```bash
# 检查真实文章数据
ls -la src/data/wechat/
cat src/data/wechat/wechat-all.json
```

### 第二步：启动开发服务器测试

```bash
pnpm dev
# 访问: http://localhost:5173/wechat/articles
```

### 第三步：添加更多文章（选择一种方式）

#### 方式A：手动逐篇添加
```bash
cd scripts
node wechat-manual-import.js
# 按提示输入文章信息
```

#### 方式B：批量CSV导入
```bash
# 1. 创建CSV文件 (articles.csv)
# 格式: 标题,链接,摘要,发布日期,分类编号
# 示例: "文章标题","https://mp.weixin.qq.com/s/xxx","文章摘要","2025-07-15","2"

# 2. 运行导入
cd scripts
node wechat-manual-import.js
# 选择 "2. 从CSV文件导入"
```

#### 方式C：微信API自动获取（如果有权限）
```bash
# 1. 设置环境变量
export WECHAT_APP_ID="your_app_id"
export WECHAT_APP_SECRET="your_app_secret"

# 2. 运行API爬虫
cd scripts
node wechat-api-crawler.js
```

### 第四步：配置服务器自动更新

#### 选项A：定时手动更新（推荐）
```bash
# 每周运行一次手动导入
# 添加到服务器的定时任务中
0 9 * * 1 cd /path/to/project/scripts && echo "请手动更新微信文章" | mail <EMAIL>
```

#### 选项B：API自动更新（如果有权限）
```bash
# 每天自动更新
crontab -e
# 添加：
0 8 * * * cd /path/to/project/scripts && WECHAT_CRAWLER_METHOD=api ./update-wechat-articles.sh
```

## 📋 生产环境配置

### 1. 环境变量配置

创建 `.env` 文件：
```bash
# 微信公众号配置（如果使用API）
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 爬虫方式选择
WECHAT_CRAWLER_METHOD=manual  # api, web, manual
```

### 2. 服务器脚本权限

```bash
chmod +x scripts/update-wechat-articles.sh
chmod +x scripts/*.js
```

### 3. 数据目录权限

```bash
mkdir -p src/data/wechat
chmod 755 src/data/wechat
```

### 4. 日志配置

```bash
# 创建日志目录
mkdir -p logs
touch logs/wechat-update.log
```

## 🔧 维护指南

### 日常维护任务

1. **每周检查**：查看是否有新文章需要添加
2. **每月备份**：备份 `src/data/wechat/` 目录
3. **季度清理**：清理过期的备份文件

### 添加新文章流程

1. **获取文章信息**：
   - 文章标题
   - 微信文章链接
   - 简短摘要
   - 发布日期
   - 文章分类

2. **使用导入工具**：
   ```bash
   cd scripts
   node wechat-manual-import.js
   ```

3. **验证结果**：
   ```bash
   # 检查生成的文件
   cat src/data/wechat/wechat-all.json
   
   # 启动开发服务器验证
   pnpm dev
   ```

### 故障排除

#### 问题1：文章不显示
**解决**：
```bash
# 检查JSON文件格式
node -e "JSON.parse(require('fs').readFileSync('src/data/wechat/wechat-all.json'))"

# 检查文件权限
ls -la src/data/wechat/
```

#### 问题2：导入工具报错
**解决**：
```bash
# 检查依赖
cd scripts
npm list

# 重新安装依赖
npm install cheerio node-fetch
```

#### 问题3：API获取失败
**解决**：
```bash
# 检查环境变量
echo $WECHAT_APP_ID
echo $WECHAT_APP_SECRET

# 验证API权限
curl "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$WECHAT_APP_ID&secret=$WECHAT_APP_SECRET"
```

## 📊 数据格式说明

### 文章数据结构
```json
{
  "id": "唯一标识符",
  "title": "文章标题",
  "link": "微信文章链接",
  "summary": "文章摘要",
  "publishDate": "发布日期",
  "source": "数据来源",
  "category": "文章分类",
  "crawlTime": "爬取时间",
  "account": "公众号名称"
}
```

### 分类说明
- `news`: 医院新闻
- `health`: 健康科普
- `education`: 中医教育
- `hospital`: 医院介绍

## 🎯 推荐的实际部署策略

### 阶段一：立即部署（当前状态）
- ✅ 已有1篇真实文章
- ✅ 前端组件完整可用
- ✅ 可以立即上线展示

### 阶段二：内容丰富（1-2周内）
- 📝 手动添加10-20篇历史文章
- 📝 覆盖所有分类
- 📝 建立内容基础

### 阶段三：自动化（如果获得API权限）
- 🤖 配置微信API
- 🤖 实现自动更新
- 🤖 减少人工维护

### 阶段四：优化完善（持续）
- 🔧 根据使用情况优化
- 🔧 添加更多功能
- 🔧 改进用户体验

## ✅ 部署检查清单

- [ ] 真实文章数据已生成
- [ ] 前端组件正常显示
- [ ] 手动导入工具可用
- [ ] 服务器脚本权限正确
- [ ] 定时任务配置完成
- [ ] 备份策略制定
- [ ] 维护流程建立
- [ ] 故障排除文档准备

## 📞 技术支持

如果在部署过程中遇到问题，请检查：

1. **日志文件**：`scripts/wechat-update.log`
2. **数据文件**：`src/data/wechat/*.json`
3. **脚本权限**：`ls -la scripts/`
4. **依赖安装**：`npm list` in scripts directory

---

**总结**：当前系统已经可以立即部署使用，包含真实的微信文章数据和完整的前端展示功能。后续可以通过手动导入或API配置来丰富内容。
