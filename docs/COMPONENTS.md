# 可复用组件库文档

本文档介绍了项目中新创建的可复用React组件，包括使用方法、接口说明和最佳实践。

## 目录

### 内容展示组件
1. [Title - 标题组件](#title)
2. [HeroSection - 横幅组件](#herosection)
3. [ArticlesList - 文章列表](#articleslist)
4. [ArticlesGrid - 文章网格](#articlesgrid)
5. [CardComponent - 卡片组件](#cardcomponent)
6. [VideoPlayer - 视频播放器](#videoplayer)
7. [Carousel - 轮播图](#carousel)
8. [CoreValuesSection - 核心价值观](#corevaluessection)

### 导航组件
9. [Breadcrumb - 面包屑导航](#breadcrumb)
10. [CategoryTabs - 分类标签页](#categorytabs)
11. [Sidebar - 侧边栏导航](#sidebar)
12. [TopNavigation - 顶部导航](#topnavigation)
13. [MobileNavigationMenu - 移动端导航](#mobilenavigationmenu)

### 布局组件
14. [Layout - 页面布局](#layout)
15. [PageTemplate - 页面模板](#pagetemplate)
16. [Footer - 页脚组件](#footer)
17. [HorizontalScrollContainer - 水平滚动容器](#horizontalscrollcontainer)

### 功能组件
18. [LanguageSwitcherSidebar - 侧边栏语言切换](#languageswitchersidebar)
19. [LanguageSwitcherTop - 顶部语言切换](#languageswitchertop)

---

## Title

简洁的标题组件，专注于页面内容分割功能，支持基本的尺寸选择和对齐方式。

### 基本用法

```tsx
import Title from '@/components/Title';

// 基础使用
<Title
  title="接待中外元首领导人"
  subtitle="大连东海医院先后接待过萨尔瓦多总统等多位国际政要"
/>

// 带尺寸和对齐
<Title
  title="专业医疗服务"
  subtitle="为患者提供高质量的中医诊疗服务"
  size="lg"
  align="center"
/>
```

### 主要属性

- `title`: 主标题（必需）
- `subtitle`: 副标题（可选）
- `size`: 尺寸大小 ('sm' | 'md' | 'lg')，默认 'md'
- `align`: 文本对齐方式 ('left' | 'center' | 'right')，默认 'left'
- `as`: HTML标签类型 ('h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6')，默认 'h2'
- `className`: 自定义CSS类名

### 设计特点

- **简洁设计**: 专注于页面内容分割功能
- **红色主题**: 副标题使用红色突出显示，符合网站主题
- **响应式**: 在不同设备上自适应字体大小
- **语义化**: 支持自定义HTML标签，有利于SEO

### 使用场景

- 页面区块标题
- 内容分割标题
- 卡片标题
- 侧边栏标题

---

## HeroSection

横幅区域组件，用于页面顶部的主要展示区域。

### 基本用法

```tsx
import HeroSection from '@/components/HeroSection';

<HeroSection
  title="大连东海医院"
  description="致力于中国传统医学，以中医为主，中西医结合"
  backgroundImage="https://example.com/image.jpg"
  theme="red"
  height="lg"
  button={{
    text: '了解更多',
    variant: 'secondary',
    icon: ArrowRight,
    onClick: () => console.log('Button clicked')
  }}
/>
```

### 主要属性

- `title`: 主标题（必需）
- `description`: 副标题/描述
- `backgroundImage`: 背景图片URL
- `theme`: 颜色主题 ('red' | 'blue' | 'green' | 'purple' | 'gray')
- `height`: 高度设置 ('sm' | 'md' | 'lg' | 'xl' | 'auto')
- `button`: 按钮配置对象

---

## Breadcrumb

面包屑导航组件，用于显示当前页面在网站层级中的位置。

### 基本用法

```tsx
import Breadcrumb, { BreadcrumbItem } from '@/components/Breadcrumb';

const breadcrumbItems: BreadcrumbItem[] = [
  { label: '首页', href: '/' },
  { label: '新闻中心', href: '/articles' },
  { label: '集团新闻', active: true }
];

<Breadcrumb items={breadcrumbItems} />
```

### 主要属性

- `items`: 面包屑项目列表（必需）
- `separator`: 分隔符，默认为 '>'
- `showHomeIcon`: 是否显示首页图标
- `prefix`: 前缀文本，默认为 '当前位置：'

---

## CategoryTabs

分类标签页组件，用于内容分类筛选。

### 基本用法

```tsx
import CategoryTabs, { CategoryItem } from '@/components/CategoryTabs';

const categories: CategoryItem[] = [
  { id: 'medical', label: '医疗行业' },
  { id: 'tech', label: '科技行业' }
];

<CategoryTabs
  categories={categories}
  theme="red"
  onCategoryChange={(category) => setSelectedCategory(category.id)}
/>
```

### 主要属性

- `categories`: 分类列表（必需）
- `theme`: 颜色主题
- `onCategoryChange`: 分类变化回调
- `showAllOption`: 是否显示全部选项

---

## ArticlesList

文章列表组件，横向布局展示文章。

### 基本用法

```tsx
import ArticlesList, { Article } from '@/components/ArticlesList';

const articles: Article[] = [
  {
    id: 1,
    title: '文章标题',
    excerpt: '文章摘要...',
    date: '2024-01-01',
    views: 100,
    category: '新闻',
    image: 'https://example.com/image.jpg'
  }
];

<ArticlesList
  articles={articles}
  theme="red"
  showReadMore={true}
  readMoreText="阅读更多"
/>
```

### 主要属性

- `articles`: 文章列表（必需）
- `theme`: 颜色主题
- `showImage`: 是否显示图片
- `showCategory`: 是否显示分类标签
- `showReadMore`: 是否显示阅读更多按钮

---

## ArticlesGrid

文章网格组件，纵向卡片布局展示文章。

### 基本用法

```tsx
import ArticlesGrid, { Article } from '@/components/ArticlesGrid';

<ArticlesGrid
  articles={articles}
  theme="green"
  columns={{ sm: 1, md: 2, lg: 3 }}
  showReadMore={true}
/>
```

### 主要属性

- `articles`: 文章列表（必需）
- `columns`: 网格列数配置
- `theme`: 颜色主题
- `imageAspectRatio`: 图片宽高比

---

## CardComponent

简单的卡片组件，上下结构（上方图片，下方文本）。

### 基本用法

```tsx
import CardComponent from '@/components/CardComponent';

<CardComponent
  image="https://example.com/image.jpg"
  title="卡片标题"
  subtitle="副标题"
  href="/link"
  size="md"
  showHoverEffect={true}
/>
```

### 主要属性

- `image`: 图片URL（必需）
- `title`: 标题文本（必需）
- `href`: 点击跳转链接
- `size`: 卡片尺寸
- `showHoverEffect`: 是否显示悬停效果

---

## VideoPlayer

视频播放器组件，支持多种视频格式和自定义控件。

### 基本用法

```tsx
import VideoPlayer from '@/components/VideoPlayer';

<VideoPlayer
  sources="https://example.com/video.mp4"
  title="视频标题"
  poster="https://example.com/poster.jpg"
  controls={true}
  autoPlay={false}
/>
```

### 主要属性

- `sources`: 视频源（必需）
- `title`: 视频标题
- `poster`: 封面图片
- `controls`: 是否显示控制栏
- `autoPlay`: 是否自动播放

---

## Carousel

轮播图组件，左右结构（左侧图片，右侧文本）。

### 基本用法

```tsx
import Carousel, { CarouselItem } from '@/components/Carousel';

const carouselItems: CarouselItem[] = [
  {
    id: 1,
    image: 'https://example.com/image1.jpg',
    title: '轮播标题1',
    description: '轮播描述1',
    buttonText: '了解更多',
    buttonHref: '/link1'
  }
];

<Carousel
  items={carouselItems}
  autoPlay={true}
  autoPlayInterval={10000}
  theme="red"
/>
```

### 主要属性

- `items`: 轮播项目列表（必需）
- `autoPlay`: 是否自动播放
- `autoPlayInterval`: 自动播放间隔
- `theme`: 颜色主题
- `showIndicators`: 是否显示指示器

---

## CoreValuesSection

核心价值观展示组件，支持两种布局模式：企业级横向布局和默认网格布局。

### 基本用法

```tsx
import CoreValuesSection, { CoreValue } from '@/components/CoreValuesSection';

const coreValues: CoreValue[] = [
  {
    id: 1,
    title: '医者仁心',
    subtitle: '仁爱之心',
    description: '以患者为中心，用仁爱之心对待每一位患者',
    iconType: 'heart',
    color: 'red'
  },
  {
    id: 2,
    title: '精益求精',
    subtitle: '专业精神',
    description: '不断提升医疗技术，追求卓越的医疗品质',
    iconType: 'star',
    color: 'blue'
  }
];

<CoreValuesSection
  title="核心价值观"
  subtitle="我们的理念与追求"
  layout="enterprise"
  values={coreValues}
  theme="red"
  enableI18n={true}
/>
```

### 主要属性

- `title`: 标题（必需）
- `subtitle`: 副标题
- `layout`: 布局类型 ('default' | 'enterprise')
- `values`: 核心价值观数据（必需）
- `theme`: 颜色主题
- `enableI18n`: 是否启用国际化

### 布局模式

- **default**: 网格布局，适合多个价值观展示
- **enterprise**: 企业级横向布局，带连接线，适合流程展示

---

## HorizontalScrollContainer

水平滚动容器组件，支持响应式布局和滚动控制。

### 基本用法

```tsx
import HorizontalScrollContainer from '@/components/HorizontalScrollContainer';

<HorizontalScrollContainer
  showArrows={true}
  arrowTheme="red"
  scrollStep={300}
  responsive={{
    desktop: true,
    tablet: true,
    mobile: true
  }}
  gridConfig={{
    cols: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    gap: 'gap-4'
  }}
>
  {/* 子元素 */}
  <div>内容1</div>
  <div>内容2</div>
  <div>内容3</div>
</HorizontalScrollContainer>
```

### 主要属性

- `children`: 子元素（必需）
- `showArrows`: 是否显示箭头控制
- `arrowTheme`: 箭头主题色彩
- `scrollStep`: 滚动步长（像素）
- `responsive`: 响应式断点配置
- `gridConfig`: 网格布局配置（桌面端）
- `scrollConfig`: 滚动容器配置

---

## Sidebar

侧边栏导航组件，支持多级菜单和响应式设计。

### 基本用法

```tsx
import Sidebar from '@/components/Sidebar';

<Sidebar
  onClose={() => setSidebarOpen(false)}
  collapsed={false}
/>
```

### 主要属性

- `onClose`: 关闭回调函数
- `collapsed`: 是否折叠状态

### 特性

- 多级菜单展开/折叠
- 移动端友好设计
- 国际化支持
- 搜索功能
- 语言切换器集成

---

## TopNavigation

顶部导航组件，用于移动端或侧边栏折叠时的导航。

### 基本用法

```tsx
import TopNavigation from '@/components/TopNavigation';

<TopNavigation
  onExpandSidebar={() => setSidebarCollapsed(false)}
  showExpandButton={true}
/>
```

### 主要属性

- `onExpandSidebar`: 展开侧边栏回调
- `showExpandButton`: 是否显示展开按钮

---

## MobileNavigationMenu

移动端导航菜单组件，使用shadcn/ui Menubar实现。

### 基本用法

```tsx
import MobileNavigationMenu from '@/components/MobileNavigationMenu';

<MobileNavigationMenu />
```

### 特性

- 完整的导航结构
- 红色主题设计
- 国际化支持
- 响应式宽度调整

---

## Layout

页面布局组件，管理侧边栏、顶部导航和主内容区域。

### 基本用法

```tsx
import Layout from '@/components/Layout';

<Layout>
  {/* 页面内容 */}
</Layout>
```

### 特性

- 响应式布局管理
- 侧边栏状态控制
- 移动端适配
- 自动切换导航模式

---

## PageTemplate

页面模板组件，提供统一的页面结构和样式。

### 基本用法

```tsx
import PageTemplate from '@/components/PageTemplate';

<PageTemplate
  title="页面标题"
  subtitle="页面副标题"
  breadcrumbs={breadcrumbs}
  showHero={true}
  hero={{
    title: "Hero标题",
    description: "Hero描述",
    backgroundImage: "背景图片URL",
    theme: "red",
    height: "md"
  }}
>
  {/* 页面内容 */}
</PageTemplate>
```

### 主要属性

- `title`: 页面标题
- `subtitle`: 页面副标题
- `breadcrumbs`: 面包屑导航数据
- `showHero`: 是否显示Hero区域
- `hero`: Hero区域配置

---

## Footer

页脚组件，包含导航链接、联系信息和版权信息。

### 基本用法

```tsx
import Footer from '@/components/Footer';

<Footer />
```

### 特性

- 三栏布局设计
- 快速导航链接
- 联系信息展示
- 滚动到顶部功能
- 响应式设计

---

## LanguageSwitcherSidebar

侧边栏语言切换组件，按钮式设计。

### 基本用法

```tsx
import LanguageSwitcherSidebar from '@/components/LanguageSwitcherSidebar';

<LanguageSwitcherSidebar />
```

### 特性

- 支持中文、英文、俄文
- 按钮式交互
- 当前语言高亮显示

---

## LanguageSwitcherTop

顶部语言切换组件，下拉选择式设计。

### 基本用法

```tsx
import LanguageSwitcherTop from '@/components/LanguageSwitcherTop';

<LanguageSwitcherTop />
```

### 特性

- 下拉选择式交互
- 紧凑的设计
- 自定义下拉箭头

---

## 最佳实践

### 1. 主题一致性
- 在同一页面中使用相同的主题颜色
- 推荐使用项目默认的红色主题

### 2. 响应式设计
- 所有组件都支持响应式设计
- 在移动端会自动调整布局和尺寸

### 3. 国际化支持
- 大部分组件支持国际化
- 设置 `enableI18n={true}` 启用国际化功能

### 4. 性能优化
- 图片使用 `loading="lazy"` 懒加载
- 大列表数据建议分页处理

### 5. 可访问性
- 组件包含适当的 ARIA 标签
- 支持键盘导航

---

## 技术栈

- **React 18+**: 基础框架
- **TypeScript**: 类型安全
- **TailwindCSS**: 样式系统
- **shadcn/ui**: UI组件库
- **Lucide React**: 图标库
- **react-i18next**: 国际化支持

## shadcn/ui 组件

项目还集成了shadcn/ui组件库，提供了丰富的基础UI组件：

### 表单组件
- `Button` - 按钮组件
- `Input` - 输入框组件
- `Textarea` - 文本域组件
- `Select` - 选择器组件
- `Checkbox` - 复选框组件
- `Radio Group` - 单选按钮组组件
- `Form` - 表单组件

### 导航组件
- `Navigation Menu` - 导航菜单
- `Menubar` - 菜单栏
- `Breadcrumb` - 面包屑导航
- `Tabs` - 标签页组件

### 反馈组件
- `Alert` - 警告提示
- `Toast` - 消息提示
- `Dialog` - 对话框
- `Alert Dialog` - 确认对话框

### 数据展示
- `Table` - 表格组件
- `Card` - 卡片组件
- `Badge` - 徽章组件
- `Avatar` - 头像组件
- `Skeleton` - 骨架屏

### 布局组件
- `Sheet` - 抽屉组件
- `Sidebar` - 侧边栏
- `Resizable` - 可调整大小组件
- `Scroll Area` - 滚动区域

### 使用方式

```tsx
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
  </CardHeader>
  <CardContent>
    <Input placeholder="请输入内容" />
    <Button>提交</Button>
  </CardContent>
</Card>
```

---

## 数据管理架构

### 数据层
- `src/data/articles.json` - 文章数据
- `src/data/pageContent.json` - 页面内容配置

### 逻辑层
- `src/hooks/useArticles.ts` - 文章数据管理
- `src/hooks/usePageContent.ts` - 页面内容管理

### 使用示例
```tsx
// 在页面中使用数据管理层
const { articles, categories, loading } = useArticles({
  categoryId: selectedCategory === 'all' ? undefined : selectedCategory
});
const { getPageContent } = usePageContent();
const pageContent = getPageContent('home');
```

## 演示页面

访问 `/data-demo` 查看数据管理层的完整演示，包括：
- 搜索和筛选功能
- 分页和加载状态
- 数据统计和配置展示

## 组件分类说明

### 内容展示组件
专注于内容展示和用户交互的组件，包括文章列表、卡片、轮播图等。

### 导航组件
处理页面导航和用户路由的组件，包括侧边栏、面包屑、分类标签等。

### 布局组件
管理页面整体布局和结构的组件，包括页面模板、布局容器等。

### 功能组件
提供特定功能的组件，如语言切换、滚动容器等。

## 组件设计原则

### 1. 一致性
- 所有组件都遵循统一的设计语言
- 使用一致的颜色主题系统
- 保持相似的API设计模式

### 2. 可复用性
- 组件应该是高度可配置的
- 支持多种使用场景
- 避免硬编码特定业务逻辑

### 3. 响应式设计
- 所有组件都应支持移动端
- 使用TailwindCSS响应式类名
- 考虑不同屏幕尺寸的用户体验

### 4. 可访问性
- 包含适当的ARIA标签
- 支持键盘导航
- 考虑屏幕阅读器用户

### 5. 性能优化
- 使用React.memo优化重渲染
- 图片懒加载
- 避免不必要的DOM操作

## 贡献指南

1. 所有新组件都应该包含完整的TypeScript接口
2. 组件应该支持主题系统（red/blue/green/purple/gray）
3. 添加适当的默认值和错误处理
4. 使用数据管理层而非硬编码数据
5. 支持国际化（enableI18n属性）
6. 包含完整的JSDoc注释
7. 更新此文档以包含新组件的使用说明
8. 添加适当的单元测试
9. 确保组件在不同设备上的表现一致
